import { useState, useEffect } from "react";
import * as DialogPrimitive from "@radix-ui/react-dialog";
import { Dialog, DialogHeader, DialogTitle, DialogPortal, DialogOverlay } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import { Check, ShoppingCart, X, Minus, Plus, User, Phone, MapPin } from "lucide-react";
import { algeriaWilayas, getCitiesByWilayaId, getDeliveryPricesByWilayaId } from "@/data/algeria-locations";
import { cn } from "@/lib/utils";

interface AddBuyerDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  productId: string;
  productName: string;
  productPrice: number;
  availableColors?: string[];
  availableSizes?: string[];
  productImage?: string;
}

// Custom DialogContent without default close button
const CustomDialogContent = ({ className, children, ...props }: React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>) => (
  <DialogPortal>
    <DialogOverlay />
    <DialogPrimitive.Content
      className={cn(
        "fixed left-[50%] top-[50%] z-50 grid w-[calc(100%-32px)] max-w-lg max-h-[calc(100vh-40px)] translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg overflow-hidden",
        className
      )}
      {...props}
    >
      {children}
    </DialogPrimitive.Content>
  </DialogPortal>
);

const AddBuyerDialog = ({
  open,
  onOpenChange,
  productId,
  productName,
  productPrice,
  availableColors = [],
  availableSizes = [],
  productImage
}: AddBuyerDialogProps) => {
  // Form state
  const [buyerName, setBuyerName] = useState("");
  const [buyerPhone, setBuyerPhone] = useState("");
  const [selectedWilaya, setSelectedWilaya] = useState<number | null>(null);
  const [selectedCity, setSelectedCity] = useState<number | null>(null);
  const [address, setAddress] = useState("");
  const [deliveryType, setDeliveryType] = useState<"home" | "desk">("home");
  const [selectedColor, setSelectedColor] = useState<string | null>(null);
  const [selectedSize, setSelectedSize] = useState<string | null>(null);
  const [notes, setNotes] = useState("");
  const [quantity, setQuantity] = useState(1);
  const [isMobile, setIsMobile] = useState(false);

  // Form submission state
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Derived state
  const [cities, setCities] = useState<{ id: number; name: string }[]>([]);
  const [deliveryPrice, setDeliveryPrice] = useState<number>(0);
  const [itemPrice, setItemPrice] = useState<number>(productPrice);
  const [totalPrice, setTotalPrice] = useState<number>(productPrice);

  // Fallback image
  const fallbackImage = `https://picsum.photos/seed/${productId}/200/200`;

  // Check if device is mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 640);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  // Check if all required fields are filled
  const isFormValid = () => {
    const basicFieldsValid = buyerName.trim() !== "" &&
                            buyerPhone.trim() !== "" &&
                            selectedWilaya !== null &&
                            selectedCity !== null;

    // For home delivery, address is also required
    if (deliveryType === "home") {
      return basicFieldsValid && address.trim() !== "";
    }

    // For desk delivery, address is not required
    return basicFieldsValid;
  };



  // Reset form when dialog closes
  useEffect(() => {
    if (!open) {
      setBuyerName("");
      setBuyerPhone("");
      setSelectedWilaya(null);
      setSelectedCity(null);
      setAddress("");
      setDeliveryType("home");
      setSelectedColor(null);
      setSelectedSize(null);
      setQuantity(1);
      setIsSubmitting(false);
    }
  }, [open]);

  // Clear address when delivery type changes to desk
  useEffect(() => {
    if (deliveryType === "desk") {
      setAddress("");
    }
  }, [deliveryType]);

  // Update cities when wilaya changes
  useEffect(() => {
    if (selectedWilaya) {
      const citiesForWilaya = getCitiesByWilayaId(selectedWilaya);
      setCities(citiesForWilaya);
      setSelectedCity(null);

      // Update delivery price
      const prices = getDeliveryPricesByWilayaId(selectedWilaya);
      setDeliveryPrice(deliveryType === "home" ? prices.home : prices.desk);
    } else {
      setCities([]);
      setDeliveryPrice(0);
    }
  }, [selectedWilaya, deliveryType]);

  // Update item price when quantity changes
  useEffect(() => {
    setItemPrice(productPrice * quantity);
  }, [productPrice, quantity]);

  // Update total price when delivery price or item price changes
  useEffect(() => {
    setTotalPrice(itemPrice + deliveryPrice);
  }, [itemPrice, deliveryPrice]);

  // Update delivery price when delivery type changes
  useEffect(() => {
    if (selectedWilaya) {
      const prices = getDeliveryPricesByWilayaId(selectedWilaya);
      setDeliveryPrice(deliveryType === "home" ? prices.home : prices.desk);
    }
  }, [deliveryType, selectedWilaya]);

  // Quantity handlers
  const incrementQuantity = () => {
    setQuantity(prev => prev + 1);
  };

  const decrementQuantity = () => {
    setQuantity(prev => (prev > 1 ? prev - 1 : 1));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!buyerName || !buyerPhone || !selectedWilaya || !selectedCity) {
      toast.error("Please fill in all required fields");
      return;
    }

    // Address is only required for home delivery
    if (deliveryType === "home" && !address) {
      toast.error("Please enter a detailed address for home delivery");
      return;
    }

    setIsSubmitting(true);

    try {
      // In a real app, this would send data to the server
      const buyerData = {
        name: buyerName,
        phone: buyerPhone,
        wilaya: algeriaWilayas.find(w => w.id === selectedWilaya)?.name,
        city: cities.find(c => c.id === selectedCity)?.name,
        address,
        deliveryType,
        deliveryPrice,
        quantity,
        itemPrice,
        totalPrice,
        color: selectedColor,
        size: selectedSize,
        notes,
        productId,
        productName
      };

      console.log("Buyer data:", buyerData);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      toast.success(`Order for ${buyerName} added successfully!`);

      // Close dialog (form will be reset by useEffect)
      onOpenChange(false);
    } catch (error) {
      toast.error("Failed to add buyer. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <CustomDialogContent className="sm:max-w-[600px] w-[calc(100%-32px)] max-w-[95vw] max-h-[90vh] flex flex-col bg-white p-0 overflow-hidden">
        <DialogHeader className="border-b border-gray-300 pb-4 bg-white flex-shrink-0 px-6 pt-6 relative">
          <DialogTitle className="text-xl flex items-center gap-2 text-black pr-10">
            <User className="h-6 w-6 text-black" />
            Add Buyer
          </DialogTitle>
          {/* Custom close button to ensure proper positioning */}
          <button
            onClick={() => onOpenChange(false)}
            className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none p-1 hover:bg-gray-100"
          >
            <X className="h-4 w-4 text-black" />
            <span className="sr-only">Close</span>
          </button>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto scrollbar-hide">
          <div className="space-y-6 py-4 px-6">

            {/* Product Summary */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center gap-2 text-black">
                <ShoppingCart className="h-5 w-5 text-black" />
                Product Information
              </h3>
              <div className="border border-gray-300 rounded-lg p-4 bg-white">
                <div className="flex flex-wrap sm:flex-nowrap items-center gap-3">
                  <div className="relative h-16 w-16 flex-shrink-0 rounded-md overflow-hidden border border-gray-300">
                    <img
                      src={productImage || fallbackImage}
                      alt={productName}
                      className="h-full w-full object-contain"
                      onError={(e) => {
                        e.currentTarget.src = fallbackImage;
                      }}
                    />
                  </div>
                  <div className="flex-1 min-w-0 w-[calc(100%-80px)] sm:w-auto">
                    <h4 className="font-medium text-black">{productName}</h4>
                    <p className="text-sm text-gray-600">${productPrice.toFixed(2)}</p>
                  </div>
                  <div className="flex items-center border border-gray-300 rounded-md mt-2 sm:mt-0 ml-auto">
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 rounded-none hover:bg-gray-100"
                      onClick={decrementQuantity}
                      disabled={quantity <= 1}
                    >
                      <Minus className="h-3 w-3" />
                    </Button>
                    <span className="w-8 text-center text-sm text-black">{quantity}</span>
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 rounded-none hover:bg-gray-100"
                      onClick={incrementQuantity}
                    >
                      <Plus className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Buyer Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold flex items-center gap-2 text-black">
                  <User className="h-5 w-5 text-black" />
                  Buyer Information
                </h3>
                <div className="border border-gray-300 rounded-lg p-4 bg-white">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="buyerName" className="text-black">
                        Name <span className="text-red-500">*</span>
                      </Label>
                      <div className="relative">
                        <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <Input
                          id="buyerName"
                          value={buyerName}
                          onChange={(e) => setBuyerName(e.target.value)}
                          placeholder="Enter buyer's name"
                          className="pl-10 border-gray-300 focus:border-black"
                          required
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="buyerPhone" className="text-black">
                        Phone <span className="text-red-500">*</span>
                      </Label>
                      <div className="relative">
                        <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <Input
                          id="buyerPhone"
                          value={buyerPhone}
                          onChange={(e) => setBuyerPhone(e.target.value)}
                          placeholder="Enter buyer's phone number"
                          className="pl-10 border-gray-300 focus:border-black"
                          required
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Location Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold flex items-center gap-2 text-black">
                  <MapPin className="h-5 w-5 text-black" />
                  Location Information
                </h3>
                <div className="border border-gray-300 rounded-lg p-4 bg-white space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="wilaya" className="text-black">
                        Wilaya <span className="text-red-500">*</span>
                      </Label>
                      <Select
                        value={selectedWilaya?.toString() || ""}
                        onValueChange={(value) => setSelectedWilaya(parseInt(value))}
                      >
                        <SelectTrigger id="wilaya" className="border-gray-300 focus:border-black">
                          <SelectValue placeholder="Select wilaya" />
                        </SelectTrigger>
                        <SelectContent className="max-h-[40vh] bg-white border border-gray-300">
                          {algeriaWilayas.map((wilaya) => (
                            <SelectItem key={wilaya.id} value={wilaya.id.toString()}>
                              {wilaya.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="city" className="text-black">
                        City <span className="text-red-500">*</span>
                      </Label>
                      <Select
                        value={selectedCity?.toString() || ""}
                        onValueChange={(value) => setSelectedCity(parseInt(value))}
                        disabled={!selectedWilaya}
                      >
                        <SelectTrigger id="city" className="border-gray-300 focus:border-black">
                          <SelectValue placeholder={selectedWilaya ? "Select city" : "Select wilaya first"} />
                        </SelectTrigger>
                        <SelectContent className="max-h-[40vh] bg-white border border-gray-300">
                          {cities.map((city) => (
                            <SelectItem key={city.id} value={city.id.toString()}>
                              {city.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Delivery Type Selection */}
                  <div className="space-y-3 pt-4 border-t border-gray-200">
                    <Label className="text-black font-medium">
                      Delivery Type <span className="text-red-500">*</span>
                    </Label>
                    <RadioGroup
                      value={deliveryType}
                      onValueChange={(value) => setDeliveryType(value as "home" | "desk")}
                      className="flex flex-col space-y-3"
                    >
                      <div className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <RadioGroupItem value="home" id="home" className="border-gray-400" />
                        <Label htmlFor="home" className="cursor-pointer text-black flex-1">
                          <div className="font-medium">Home Delivery</div>
                          <div className="text-sm text-gray-600">Delivered to your doorstep</div>
                        </Label>
                      </div>
                      <div className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <RadioGroupItem value="desk" id="desk" className="border-gray-400" />
                        <Label htmlFor="desk" className="cursor-pointer text-black flex-1">
                          <div className="font-medium">Desk Delivery</div>
                          <div className="text-sm text-gray-600">Pick up from delivery center</div>
                        </Label>
                      </div>
                    </RadioGroup>
                  </div>

                  {/* Address Fields - Only show for home delivery */}
                  {deliveryType === "home" && (
                    <div className="space-y-4 pt-4 border-t border-gray-200">
                      <div className="space-y-2">
                        <Label htmlFor="address" className="text-black">
                          Detailed Address <span className="text-red-500">*</span>
                        </Label>
                        <div className="relative">
                          <MapPin className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                          <Textarea
                            id="address"
                            value={address}
                            onChange={(e) => setAddress(e.target.value)}
                            placeholder="Enter detailed address"
                            className="pl-10 border-gray-300 focus:border-black resize-none"
                            rows={3}
                          />
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Product Options */}
              {(availableColors.length > 0 || availableSizes.length > 0) && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold flex items-center gap-2 text-black">
                    <Check className="h-5 w-5 text-black" />
                    Product Options
                  </h3>
                  <div className="border border-gray-300 rounded-lg p-4 bg-white">
                    <div className="space-y-4">
                      {availableColors.length > 0 && (
                        <div className="space-y-2">
                          <Label htmlFor="color" className="text-black">Color (Optional)</Label>
                          <Select
                            value={selectedColor || ""}
                            onValueChange={setSelectedColor}
                          >
                            <SelectTrigger id="color" className="border-gray-300 focus:border-black">
                              <SelectValue placeholder="Select color" />
                            </SelectTrigger>
                            <SelectContent className="bg-white border border-gray-300">
                              {availableColors.map((color) => (
                                <SelectItem key={color} value={color}>
                                  {color}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      )}

                      {availableSizes.length > 0 && (
                        <div className="space-y-2">
                          <Label htmlFor="size" className="text-black">Size (Optional)</Label>
                          <Select
                            value={selectedSize || ""}
                            onValueChange={setSelectedSize}
                          >
                            <SelectTrigger id="size" className="border-gray-300 focus:border-black">
                              <SelectValue placeholder="Select size" />
                            </SelectTrigger>
                            <SelectContent className="bg-white border border-gray-300">
                              {availableSizes.map((size) => (
                                <SelectItem key={size} value={size}>
                                  {size}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {/* Order Summary */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold flex items-center gap-2 text-black">
                  <ShoppingCart className="h-5 w-5 text-black" />
                  Order Summary
                </h3>
                <div className="border border-gray-300 rounded-lg p-4 bg-white">
                  <div className="space-y-3">
                    <div className="flex justify-between text-black">
                      <span>Item Price:</span>
                      <span>${productPrice.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between text-black">
                      <span>Quantity:</span>
                      <span>x{quantity}</span>
                    </div>
                    <div className="flex justify-between text-black">
                      <span>Subtotal:</span>
                      <span>${itemPrice.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between text-black">
                      <span>Delivery Fee:</span>
                      <span>${(deliveryPrice / 100).toFixed(2)}</span>
                    </div>
                    <div className="border-t border-gray-200 pt-3">
                      <div className="flex justify-between font-bold text-lg text-black">
                        <span>Total:</span>
                        <span>${((itemPrice) + (deliveryPrice / 100)).toFixed(2)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>

        {/* Fixed Bottom Action Buttons - Always Visible */}
        <div className="border-t border-gray-300 bg-white p-4 flex items-center justify-end gap-2 flex-shrink-0">
          <Button
            onClick={() => onOpenChange(false)}
            disabled={isSubmitting}
            variant="outline"
            size="sm"
            className={`border-gray-300 text-black ${
              isSubmitting
                ? 'opacity-50 cursor-not-allowed hover:bg-white'
                : 'hover:bg-gray-100'
            }`}
          >
            <X className="h-4 w-4 mr-1" />
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={!isFormValid() || isSubmitting}
            size="sm"
            className={`border-black ${
              !isFormValid() || isSubmitting
                ? 'bg-gray-400 hover:bg-gray-400 text-gray-200 cursor-not-allowed border-gray-400'
                : 'bg-black hover:bg-gray-800 text-white'
            }`}
          >
            {isSubmitting ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            ) : (
              <ShoppingCart className="h-4 w-4 mr-1" />
            )}
            Place Order
          </Button>
        </div>
      </CustomDialogContent>
    </Dialog>
  );
};

export default AddBuyerDialog;
