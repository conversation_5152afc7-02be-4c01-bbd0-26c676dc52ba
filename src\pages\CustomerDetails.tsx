import { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import Layout from '@/components/layout/Layout';
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import * as DialogPrimitive from "@radix-ui/react-dialog";
import {
  Dialog,
  DialogHeader,
  DialogTitle,
  DialogPortal,
  DialogOverlay,
} from "@/components/ui/dialog";
import { cn } from "@/lib/utils";

import {
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  DollarSign,
  ShoppingBag,
  Package,
  TrendingUp,
  CreditCard,
  Eye,
  Clock,
  Truck,
  CheckCircle,
  ArrowLeft,
  XCircle,
  X,
  ExternalLink
} from "lucide-react";
import { formatCurrency } from '@/lib/utils';
import { toast } from 'sonner';

interface OrderItem {
  id: string;
  productName: string;
  productImage: string;
  quantity: number;
  price: number;
  total: number;
}

interface CustomerOrder {
  id: string;
  orderDate: string;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  total: number;
  items: number;
  paymentMethod: string;
  orderItems: OrderItem[];
  shippingCost: number;
  notes?: string;
}

interface PaymentTransaction {
  id: string;
  date: string;
  amount: number;
  paymentMethod: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  orderId?: string;
  description: string;
}

interface RecentOrder {
  id: string;
  productName: string;
  productImage: string;
  orderDate: string;
  amount: number;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
}

interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  location: string;
  address?: string;
  joinDate: string;
  totalOrders: number;
  totalSpent: number;
  status: 'active' | 'inactive' | 'blocked';
  avatar?: string;
  recentOrders?: RecentOrder[];
  lastOrderDate?: string;
  averageOrderValue?: number;
  // Extended customer information
  orderHistory?: CustomerOrder[];
  paymentHistory?: PaymentTransaction[];
  registrationDate?: string;
}

interface OrderDetailsPopupProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  order: CustomerOrder | null;
}

// Custom DialogContent without default close button
const CustomDialogContent = ({ className, children, ...props }: React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>) => (
  <DialogPortal>
    <DialogOverlay />
    <DialogPrimitive.Content
      className={cn(
        "fixed left-[50%] top-[50%] z-50 grid w-[calc(100%-32px)] max-w-lg max-h-[calc(100vh-40px)] translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg overflow-hidden",
        className
      )}
      {...props}
    >
      {children}
    </DialogPrimitive.Content>
  </DialogPortal>
);

const OrderDetailsPopup = ({ open, onOpenChange, order }: OrderDetailsPopupProps) => {
  if (!order) return null;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'shipped':
        return 'bg-purple-100 text-purple-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const subtotal = order.orderItems.reduce((sum, item) => sum + item.total, 0);
  // Calculate affiliate earnings (example: 10% commission on subtotal)
  const affiliateEarnings = subtotal * 0.10;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <CustomDialogContent className="sm:max-w-[800px] w-[calc(100%-32px)] max-w-[95vw] max-h-[90vh] flex flex-col bg-white p-0 overflow-hidden">
        <DialogHeader className="border-b border-gray-300 pb-4 bg-white flex-shrink-0 px-6 pt-6 relative">
          <DialogTitle className="text-xl flex items-center gap-2 text-black pr-10">
            <Package className="h-6 w-6 text-black" />
            Order Details - {order.id}
          </DialogTitle>
          {/* Custom close button to ensure proper positioning */}
          <button
            onClick={() => onOpenChange(false)}
            className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none p-1 hover:bg-gray-100"
          >
            <X className="h-4 w-4 text-black" />
            <span className="sr-only">Close</span>
          </button>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto">
          <div className="space-y-6 py-4 px-6">
            {/* Order Items - First */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center gap-2 text-black">
                <Package className="h-5 w-5 text-black" />
                Order Items ({order.items})
              </h3>
              <div className="border border-gray-300 rounded-lg overflow-hidden bg-white">
                <div className="space-y-0">
                  {order.orderItems.map((item, index) => (
                    <div key={item.id} className={`flex items-center gap-4 p-4 ${index !== order.orderItems.length - 1 ? 'border-b border-gray-200' : ''}`}>
                      <img
                        src={item.productImage}
                        alt={item.productName}
                        className="w-16 h-16 object-cover rounded-md border border-gray-300"
                      />
                      <div className="flex-1">
                        <h4 className="font-medium text-black">{item.productName}</h4>
                        <p className="text-sm text-gray-600">
                          Quantity: {item.quantity} × {formatCurrency(item.price)}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-black">{formatCurrency(item.total)}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Order Summary with Status - Second */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center gap-2 text-black">
                <CreditCard className="h-5 w-5 text-black" />
                Order Summary
              </h3>
              <div className="p-4 border border-gray-300 rounded-lg space-y-4 bg-white">
                {/* Status Section */}
                <div className="border-b border-gray-200 pb-4">
                  <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
                    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium text-black">Status:</span>
                        <Badge className={`${getStatusColor(order.status)} border-0 px-3 py-1.5 text-sm font-medium`}>
                          {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <Calendar className="h-4 w-4" />
                        <span>Ordered on {new Date(order.orderDate).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Financial Summary */}
                <div className="space-y-3">
                  <div className="flex justify-between text-black">
                    <span>Subtotal:</span>
                    <span>{formatCurrency(subtotal)}</span>
                  </div>
                  <div className="flex justify-between text-black">
                    <span>Shipping:</span>
                    <span>{formatCurrency(order.shippingCost)}</span>
                  </div>
                  <div className="flex justify-between text-black">
                    <span>Earn:</span>
                    <span className="text-green-600 font-medium">{formatCurrency(affiliateEarnings)}</span>
                  </div>
                  <div className="border-t border-gray-200 pt-3">
                    <div className="flex justify-between font-bold text-lg text-black">
                      <span>Total:</span>
                      <span>{formatCurrency(order.total)}</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <CreditCard size={16} />
                    <span>Payment Method: {order.paymentMethod}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Order Notes - Third (if available) */}
            {order.notes && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold flex items-center gap-2 text-black">
                  <Package className="h-5 w-5 text-black" />
                  Order Notes
                </h3>
                <div className="border border-gray-300 rounded-lg p-4 bg-white">
                  <p className="text-sm text-gray-600">{order.notes}</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </CustomDialogContent>
    </Dialog>
  );
};

const CustomerDetails = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedOrder, setSelectedOrder] = useState<CustomerOrder | null>(null);
  const [showOrderDetails, setShowOrderDetails] = useState(false);

  // Mock data - replace with actual API call
  useEffect(() => {
    const fetchCustomer = async () => {
      setLoading(true);
      try {
        // Mock customer data - replace with actual API call
        const mockCustomer: Customer = {
          id: id || 'CUST-001',
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '+****************',
          location: 'New York, USA',
          address: '123 Main Street, New York, NY 10001',
          joinDate: '2023-12-15',
          totalOrders: 12,
          totalSpent: 1299.99,
          status: 'active',
          orderHistory: [
            {
              id: 'ORD-001',
              orderDate: '2024-01-15',
              status: 'delivered',
              total: 299.99,
              items: 3,
              paymentMethod: 'Credit Card',
              shippingCost: 15.00,
              notes: 'Customer requested express delivery',
              orderItems: [
                {
                  id: 'item-1',
                  productName: 'Wireless Headphones',
                  productImage: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=100&h=100&fit=crop',
                  quantity: 1,
                  price: 199.99,
                  total: 199.99
                },
                {
                  id: 'item-2',
                  productName: 'Phone Case',
                  productImage: 'https://images.unsplash.com/photo-1556656793-08538906a9f8?w=100&h=100&fit=crop',
                  quantity: 2,
                  price: 50.00,
                  total: 100.00
                }
              ]
            },
            {
              id: 'ORD-002',
              orderDate: '2024-01-08',
              status: 'shipped',
              total: 149.99,
              items: 1,
              paymentMethod: 'PayPal',
              shippingCost: 10.00,
              notes: 'Gift wrapping requested',
              orderItems: [
                {
                  id: 'item-3',
                  productName: 'Bluetooth Speaker',
                  productImage: 'https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=100&h=100&fit=crop',
                  quantity: 1,
                  price: 139.99,
                  total: 139.99
                }
              ]
            },
            {
              id: 'ORD-003',
              orderDate: '2023-12-20',
              status: 'cancelled',
              total: 89.99,
              items: 2,
              paymentMethod: 'Credit Card',
              shippingCost: 5.00,
              notes: 'Customer cancelled due to change of mind',
              orderItems: [
                {
                  id: 'item-4',
                  productName: 'USB Cable',
                  productImage: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=100&h=100&fit=crop',
                  quantity: 2,
                  price: 42.50,
                  total: 85.00
                }
              ]
            }
          ],
          paymentHistory: [
            {
              id: 'pay-001',
              date: '2024-01-15',
              amount: 299.99,
              paymentMethod: 'Credit Card',
              status: 'completed',
              orderId: 'ORD-001',
              description: 'Order Payment'
            },
            {
              id: 'pay-002',
              date: '2024-01-08',
              amount: 149.99,
              paymentMethod: 'PayPal',
              status: 'completed',
              orderId: 'ORD-002',
              description: 'Order Payment'
            },
            {
              id: 'pay-003',
              date: '2023-12-20',
              amount: 89.99,
              paymentMethod: 'Credit Card',
              status: 'refunded',
              orderId: 'ORD-003',
              description: 'Order Refund'
            }
          ]
        };
        
        setCustomer(mockCustomer);
      } catch (error) {
        console.error('Error fetching customer:', error);
        toast.error('Failed to load customer details');
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchCustomer();
    }
  }, [id]);

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black"></div>
        </div>
      </Layout>
    );
  }

  if (!customer) {
    return (
      <Layout>
        <div className="flex flex-col items-center justify-center h-64 space-y-4">
          <h2 className="text-xl font-semibold text-black">Customer not found</h2>
          <Button onClick={() => navigate('/customers')} variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Customers
          </Button>
        </div>
      </Layout>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-yellow-100 text-yellow-800';
      case 'blocked':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getOrderStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'shipped':
        return 'bg-purple-100 text-purple-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getOrderStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock size={14} className="text-yellow-600" />;
      case 'processing':
        return <Package size={14} className="text-blue-600" />;
      case 'shipped':
        return <Truck size={14} className="text-purple-600" />;
      case 'delivered':
        return <CheckCircle size={14} className="text-green-600" />;
      case 'cancelled':
        return <XCircle size={14} className="text-red-600" />;
      default:
        return <Clock size={14} className="text-gray-500" />;
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'refunded':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleViewOrder = (order: CustomerOrder) => {
    setSelectedOrder(order);
    setShowOrderDetails(true);
  };



  const averageOrderValue = customer.averageOrderValue || (customer.totalSpent / customer.totalOrders);

  return (
    <Layout>
      <div className="max-w-5xl mx-auto space-y-4 sm:space-y-6 overflow-hidden px-4 sm:px-0">
        {/* Header */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-4 w-full sm:w-auto">
            <Button
              onClick={() => navigate('/customers')}
              variant="outline"
              size="sm"
              className="border-gray-300 text-black hover:bg-gray-100 w-full sm:w-auto"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Customers
            </Button>
            <div className="w-full sm:w-auto">
              <h1 className="text-xl sm:text-2xl font-bold text-black flex items-center gap-2 break-words">
                <User className="h-5 w-5 sm:h-6 sm:w-6 text-black flex-shrink-0" />
                <span className="break-all">  Customer Information
                </span>
              </h1>
            </div>
          </div>
        </div>

        {/* Customer Information - First */}
        <div className="space-y-3 sm:space-y-4">
      
          <div className="border border-gray-300 rounded-lg p-4 sm:p-6 bg-white shadow-sm">
            <div className="space-y-4">
              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 pb-4 border-b border-gray-200">
                <Avatar className="h-16 w-16 sm:h-16 sm:w-16 border-2 border-gray-300 mx-auto sm:mx-0">
                  <AvatarImage src={customer.avatar} />
                  <AvatarFallback className="bg-gray-100 text-black text-xl font-semibold">
                    {customer.name.split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 text-center sm:text-left w-full">
                  <p className="font-semibold text-lg sm:text-xl text-black break-words">{customer.name}</p>
                  <p className="text-sm text-gray-600 mt-1 break-all">Customer ID: {customer.id}</p>
                  <div className="flex flex-col sm:flex-row items-center justify-center sm:justify-start gap-2 mt-2">
                    <span className="text-sm font-medium text-black">Status:</span>
                    <Badge className={`${getStatusColor(customer.status)} border-0 px-3 py-1 text-sm font-medium`}>
                      {customer.status.charAt(0).toUpperCase() + customer.status.slice(1)}
                    </Badge>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                <div className="space-y-3">
                  <div className="flex items-start gap-3">
                    <Mail size={16} className="text-gray-600 flex-shrink-0 mt-0.5" />
                    <span className="text-sm text-black break-all">{customer.email}</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Phone size={16} className="text-gray-600 flex-shrink-0" />
                    <span className="text-sm text-black">{customer.phone}</span>
                  </div>
                  <div className="flex items-start gap-3">
                    <MapPin size={16} className="text-gray-600 flex-shrink-0 mt-0.5" />
                    <span className="text-sm text-black break-words">{customer.location}</span>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <Calendar size={16} className="text-gray-600 flex-shrink-0" />
                    <span className="text-sm text-black">
                      Joined: {new Date(customer.joinDate).toLocaleDateString()}
                    </span>
                  </div>
                  {customer.address && (
                    <div className="flex items-start gap-3">
                      <MapPin size={16} className="text-gray-600 flex-shrink-0 mt-0.5" />
                      <span className="text-sm text-black break-words">{customer.address}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Customer Summary - Second */}
        <div className="space-y-3 sm:space-y-4">
          <h3 className="text-lg font-semibold flex items-center gap-2 text-black px-1 sm:px-0">
            <TrendingUp className="h-5 w-5 text-black" />
            Customer Summary
          </h3>
          <div className="p-4 sm:p-6 border border-gray-300 rounded-lg space-y-4 sm:space-y-6 bg-white shadow-sm">
            {/* Customer Statistics */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6">
              <div className="bg-gray-50 rounded-lg p-3 sm:p-4 border border-gray-200">
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-3 mb-2">
                  <ShoppingBag size={18} className="text-blue-600 flex-shrink-0" />
                  <span className="text-xs sm:text-sm font-medium text-gray-600 leading-tight">Total Orders</span>
                </div>
                <p className="text-xl sm:text-2xl font-bold text-black">{customer.totalOrders}</p>
              </div>

              <div className="bg-gray-50 rounded-lg p-3 sm:p-4 border border-gray-200">
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-3 mb-2">
                  <DollarSign size={18} className="text-green-600 flex-shrink-0" />
                  <span className="text-xs sm:text-sm font-medium text-gray-600 leading-tight">Total Spent</span>
                </div>
                <p className="text-lg sm:text-2xl font-bold text-black break-all">{formatCurrency(customer.totalSpent)}</p>
              </div>

              <div className="bg-gray-50 rounded-lg p-3 sm:p-4 border border-gray-200">
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-3 mb-2">
                  <TrendingUp size={18} className="text-purple-600 flex-shrink-0" />
                  <span className="text-xs sm:text-sm font-medium text-gray-600 leading-tight">Avg. Order Value</span>
                </div>
                <p className="text-lg sm:text-2xl font-bold text-black break-all">{formatCurrency(averageOrderValue)}</p>
              </div>

              <div className="bg-gray-50 rounded-lg p-3 sm:p-4 border border-gray-200">
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-3 mb-2">
                  <Calendar size={18} className="text-orange-600 flex-shrink-0" />
                  <span className="text-xs sm:text-sm font-medium text-gray-600 leading-tight">Last Order</span>
                </div>
                <p className="text-xs sm:text-sm font-semibold text-black leading-tight">
                  {customer.lastOrderDate
                    ? new Date(customer.lastOrderDate).toLocaleDateString()
                    : customer.orderHistory && customer.orderHistory.length > 0
                      ? new Date(customer.orderHistory[0].orderDate).toLocaleDateString()
                      : 'No orders yet'
                  }
                </p>
              </div>
            </div>

            {/* Customer Lifetime Value */}
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4 border border-blue-200">
              <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                <div className="flex items-center gap-3 w-full sm:w-auto">
                  <div className="bg-blue-100 rounded-full p-2 flex-shrink-0">
                    <DollarSign size={20} className="text-blue-600" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-sm font-medium text-gray-700">Customer Lifetime Value</p>
                    <p className="text-xl sm:text-2xl font-bold text-black break-all">{formatCurrency(customer.totalSpent)}</p>
                  </div>
                </div>
                <div className="text-left sm:text-right w-full sm:w-auto">
                  <p className="text-xs text-gray-600">Based on total spending</p>
                  <p className="text-sm font-medium text-gray-700">
                    {customer.totalOrders} orders completed
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Order History Section - Third */}
        <div className="space-y-3 sm:space-y-4">
          <h3 className="text-lg font-semibold flex items-center gap-2 text-black px-1 sm:px-0">
            <ShoppingBag className="h-5 w-5 text-black" />
            Order History ({customer.orderHistory?.length || 0})
          </h3>
          <div className="border border-gray-300 rounded-lg overflow-hidden bg-white shadow-sm">
            <div className="space-y-0 max-h-96 overflow-hidden">
              {customer.orderHistory && customer.orderHistory.length > 0 ? (
                customer.orderHistory.slice(0, 5).map((order, index) => {
                  const orderSubtotal = order.orderItems.reduce((sum, item) => sum + item.total, 0);
                  const orderEarnings = orderSubtotal * 0.10;

                  return (
                    <div
                      key={order.id}
                      className={`flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-4 p-3 sm:p-4 hover:bg-gray-50 cursor-pointer transition-colors ${index !== Math.min(customer.orderHistory!.length, 5) - 1 ? 'border-b border-gray-200' : ''}`}
                      onClick={() => handleViewOrder(order)}
                    >
                      <div className="flex items-center gap-3 w-full sm:w-auto">
                        <div className="flex items-center gap-2 flex-shrink-0">
                          <Package size={18} className="text-gray-600" />
                          <Eye size={16} className="text-blue-600" />
                        </div>
                        <div className="flex-1 min-w-0 sm:hidden">
                          <div className="flex flex-col gap-1">
                            <div className="flex items-center gap-2 flex-wrap">
                              <h4 className="font-semibold text-black text-sm break-all">Order #{order.id}</h4>
                              <Badge className={`${getOrderStatusColor(order.status)} border-0 px-2 py-1 text-xs font-medium flex items-center gap-1`}>
                                {getOrderStatusIcon(order.status)}
                                {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                              </Badge>
                            </div>
                            <p className="text-xs text-gray-600">
                              {new Date(order.orderDate).toLocaleDateString()} • {order.items} items
                            </p>
                          </div>
                        </div>
                      </div>

                      <div className="hidden sm:flex flex-1 min-w-0">
                        <div className="flex flex-col gap-1">
                          <div className="flex items-center gap-3">
                            <h4 className="font-semibold text-black break-all">Order #{order.id}</h4>
                            <Badge className={`${getOrderStatusColor(order.status)} border-0 px-2 py-1 text-xs font-medium flex items-center gap-1`}>
                              {getOrderStatusIcon(order.status)}
                              {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600">
                            {new Date(order.orderDate).toLocaleDateString()} • {order.items} items
                          </p>
                        </div>
                      </div>

                      <div className="text-left sm:text-right w-full sm:w-auto">
                        <p className="font-semibold text-black text-base sm:text-lg">{formatCurrency(order.total)}</p>
                        <p className="text-sm text-green-600 font-medium">Earn: {formatCurrency(orderEarnings)}</p>
                        <p className="text-xs text-gray-500 sm:block hidden">Click to view details</p>
                      </div>
                    </div>
                  );
                })
              ) : (
                <div className="p-6 sm:p-8 text-center text-gray-500">
                  <ShoppingBag className="h-10 w-10 sm:h-12 sm:w-12 mx-auto mb-3 text-gray-300" />
                  <p className="text-gray-600">No order history found</p>
                </div>
              )}
            </div>
          </div>
        </div>



        {/* Payment History Section - Fourth */}
        {customer.paymentHistory && customer.paymentHistory.length > 0 && (
          <div className="space-y-3 sm:space-y-4">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 px-1 sm:px-0">
              <h3 className="text-lg font-semibold flex items-center gap-2 text-black">
                <CreditCard className="h-5 w-5 text-black" />
                Payment History
              </h3>
              <Button
                onClick={() => navigate(`/payment/user/${customer.id}`)}
                className="flex items-center gap-2 w-full sm:w-auto"
                size="sm"
              >
                <ExternalLink size={16} />
                View Payment Details
              </Button>
            </div>
          </div>
        )}

      </div>

      {/* Order Details Popup */}
      <OrderDetailsPopup
        open={showOrderDetails}
        onOpenChange={setShowOrderDetails}
        order={selectedOrder}
      />

    </Layout>
  );
};

export default CustomerDetails;



