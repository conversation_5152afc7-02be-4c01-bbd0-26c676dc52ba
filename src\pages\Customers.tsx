import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Layout from '@/components/layout/Layout';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  Search, 
  Filter, 
  Eye, 
  Mail, 
  Phone, 
  MapPin,
  Calendar,
  ShoppingBag,
  DollarSign,
  Users,
  UserPlus
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { formatCurrency } from '@/lib/utils';

interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  location: string;
  joinDate: string;
  totalOrders: number;
  totalSpent: number;
  status: 'active' | 'inactive' | 'blocked';
  avatar?: string;
}

const Customers = () => {
  const navigate = useNavigate();
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Mock data - replace with actual API call
  useEffect(() => {
    const mockCustomers: Customer[] = [
      {
        id: 'CUST-001',
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+****************',
        location: 'New York, USA',
        joinDate: '2023-12-15',
        totalOrders: 12,
        totalSpent: 1299.99,
        status: 'active'
      },
      {
        id: 'CUST-002',
        name: 'Jane Smith',
        email: '<EMAIL>',
        phone: '+****************',
        location: 'Los Angeles, USA',
        joinDate: '2023-11-20',
        totalOrders: 8,
        totalSpent: 849.50,
        status: 'active'
      },
      {
        id: 'CUST-003',
        name: 'Mike Johnson',
        email: '<EMAIL>',
        phone: '+****************',
        location: 'Chicago, USA',
        joinDate: '2023-10-10',
        totalOrders: 5,
        totalSpent: 399.99,
        status: 'inactive'
      },
      {
        id: 'CUST-004',
        name: 'Sarah Wilson',
        email: '<EMAIL>',
        phone: '+****************',
        location: 'Miami, USA',
        joinDate: '2024-01-05',
        totalOrders: 15,
        totalSpent: 2199.99,
        status: 'active'
      },
      {
        id: 'CUST-005',
        name: 'David Brown',
        email: '<EMAIL>',
        phone: '+****************',
        location: 'Seattle, USA',
        joinDate: '2023-09-15',
        totalOrders: 3,
        totalSpent: 199.99,
        status: 'blocked'
      }
    ];

    setTimeout(() => {
      setCustomers(mockCustomers);
      setLoading(false);
    }, 1000);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-yellow-100 text-yellow-800';
      case 'blocked':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.name.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === 'all' || customer.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const updateCustomerStatus = (customerId: string, newStatus: string) => {
    setCustomers(customers.map(customer => 
      customer.id === customerId ? { ...customer, status: newStatus as any } : customer
    ));
    toast.success(`Customer ${customerId} status updated to ${newStatus}`);
  };

  const totalCustomers = customers.length;
  const activeCustomers = customers.filter(customer => customer.status === 'active').length;
  const totalRevenue = customers.reduce((sum, customer) => sum + customer.totalSpent, 0);
  const avgOrderValue = totalRevenue / customers.reduce((sum, customer) => sum + customer.totalOrders, 0) || 0;

  return (
    <Layout>
      <div className="mb-6">
        <h1 className="text-2xl sm:text-3xl font-bold mb-1">Customer Management</h1>
        <p className="text-sm sm:text-base text-muted-foreground">
          Manage and track your customers
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Customers</p>
                <p className="text-2xl font-bold">{totalCustomers}</p>
              </div>
              <Users className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Active Customers</p>
                <p className="text-2xl font-bold">{activeCustomers}</p>
              </div>
              <UserPlus className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Revenue</p>
                <p className="text-2xl font-bold">{formatCurrency(totalRevenue)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Avg Order Value</p>
                <p className="text-2xl font-bold">{formatCurrency(avgOrderValue)}</p>
              </div>
              <ShoppingBag className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" size={18} />
              <Input
                placeholder="Search customers by name, email, or ID..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="blocked">Blocked</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Customers Table */}
      <Card>
        <CardHeader>
          <CardTitle>Customers ({filteredCustomers.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="mt-2 text-muted-foreground">Loading customers...</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table className="w-full min-w-full">
                <TableHeader>
                  <TableRow>
                    <TableHead className="min-w-[200px]">Customer</TableHead>
                    <TableHead className="min-w-[160px] whitespace-nowrap">Contact</TableHead>
                    <TableHead className="min-w-[120px] whitespace-nowrap">Join Date</TableHead>
                    <TableHead className="min-w-[80px] whitespace-nowrap">Orders</TableHead>
                    <TableHead className="min-w-[120px] whitespace-nowrap">Total Spent</TableHead>
                    <TableHead className="min-w-[80px] whitespace-nowrap">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredCustomers.map((customer) => (
                    <TableRow key={customer.id}>
                      <TableCell className="min-w-[200px]">
                        <div className="flex items-center gap-3">
                          <Avatar>
                            <AvatarImage src={customer.avatar} />
                            <AvatarFallback>
                              {customer.name.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium whitespace-nowrap">{customer.name}</p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="min-w-[160px]">
                        <div className="flex items-center gap-2 text-sm whitespace-nowrap">
                          <Phone size={14} />
                          <span>{customer.phone}</span>
                        </div>
                      </TableCell>
                      <TableCell className="min-w-[120px]">
                        <div className="flex items-center gap-2 whitespace-nowrap">
                          <Calendar size={14} />
                          <span>{new Date(customer.joinDate).toLocaleDateString()}</span>
                        </div>
                      </TableCell>
                      <TableCell className="font-medium min-w-[80px] whitespace-nowrap">{customer.totalOrders}</TableCell>
                      <TableCell className="font-medium min-w-[120px] whitespace-nowrap">{formatCurrency(customer.totalSpent)}</TableCell>
                      <TableCell className="min-w-[80px]">
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => navigate(`/customer/${customer.id}`)}
                          >
                            <Eye size={16} />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </Layout>
  );
};

export default Customers;
