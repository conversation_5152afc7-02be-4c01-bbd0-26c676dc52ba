import { useState } from 'react';
import * as DialogPrimitive from "@radix-ui/react-dialog";
import {
  Dialog,
  DialogHeader,
  DialogTitle,
  DialogPortal,
  DialogOverlay,
} from "@/components/ui/dialog";
import { cn } from "@/lib/utils";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  DollarSign,
  ShoppingBag,
  Save,
  X,
  Package,
  TrendingUp,
  Star,
  CreditCard,
  Eye,
  Clock,
  Truck,
  CheckCircle
} from "lucide-react";
import { formatCurrency } from '@/lib/utils';
import { toast } from 'sonner';

interface OrderItem {
  id: string;
  productName: string;
  productImage: string;
  quantity: number;
  price: number;
  total: number;
}

interface AffiliateWorker {
  id: string;
  name: string;
  email: string;
  phone: string;
  commissionRate: number;
  totalEarnings: number;
  joinDate: string;
}

interface CustomerOrder {
  id: string;
  orderDate: string;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  total: number;
  items: number;
  paymentMethod: string;
  orderItems: OrderItem[];
  affiliateWorker: AffiliateWorker;
  shippingCost: number;
  notes?: string;
}

interface PaymentTransaction {
  id: string;
  date: string;
  amount: number;
  paymentMethod: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  orderId?: string;
  description: string;
}

interface RecentOrder {
  id: string;
  productName: string;
  productImage: string;
  orderDate: string;
  amount: number;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
}

interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  location: string;
  address?: string;
  joinDate: string;
  totalOrders: number;
  totalSpent: number;
  status: 'active' | 'inactive' | 'blocked';
  avatar?: string;
  recentOrders?: RecentOrder[];
  lastOrderDate?: string;
  averageOrderValue?: number;
  // Extended customer information
  orderHistory?: CustomerOrder[];
  affiliateWorker?: AffiliateWorker;
  paymentHistory?: PaymentTransaction[];
  totalCommissionEarned?: number;
  registrationDate?: string;
}

interface CustomerDetailsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  customer: Customer | null;
  onStatusUpdate: (customerId: string, newStatus: string) => void;
}

// Custom DialogContent without default close button
const CustomDialogContent = ({ className, children, ...props }: React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>) => (
  <DialogPortal>
    <DialogOverlay />
    <DialogPrimitive.Content
      className={cn(
        "fixed left-[50%] top-[50%] z-50 grid w-[calc(100%-32px)] max-w-lg max-h-[calc(100vh-40px)] translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg overflow-hidden",
        className
      )}
      {...props}
    >
      {children}
    </DialogPrimitive.Content>
  </DialogPortal>
);

const CustomerDetailsDialog = ({ open, onOpenChange, customer, onStatusUpdate }: CustomerDetailsDialogProps) => {
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const [hasStatusChanged, setHasStatusChanged] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [selectedOrderId, setSelectedOrderId] = useState<string | null>(null);

  if (!customer) return null;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-yellow-100 text-yellow-800';
      case 'blocked':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getOrderStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'shipped':
        return 'bg-purple-100 text-purple-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getOrderStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock size={14} className="text-yellow-500" />;
      case 'processing':
        return <Package size={14} className="text-blue-500" />;
      case 'shipped':
        return <Truck size={14} className="text-purple-500" />;
      case 'delivered':
        return <CheckCircle size={14} className="text-green-500" />;
      default:
        return <Clock size={14} className="text-gray-500" />;
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'refunded':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Reset status when customer changes
  if (selectedStatus !== customer.status && !hasStatusChanged) {
    setSelectedStatus(customer.status);
  }

  const handleStatusChange = (newStatus: string) => {
    setSelectedStatus(newStatus);
    setHasStatusChanged(newStatus !== customer.status);
  };

  const handleConfirmClick = () => {
    if (!hasStatusChanged) return;
    setShowConfirmDialog(true);
  };

  const handleConfirmStatusUpdate = async () => {
    setIsUpdatingStatus(true);
    setShowConfirmDialog(false);
    try {
      onStatusUpdate(customer.id, selectedStatus);
      setHasStatusChanged(false);
      toast.success(`Customer status updated to ${selectedStatus}`);
    } catch (error) {
      toast.error('Failed to update customer status');
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  const handleCancelStatusChange = () => {
    setSelectedStatus(customer.status);
    setHasStatusChanged(false);
  };

  const averageOrderValue = customer.averageOrderValue || (customer.totalSpent / customer.totalOrders);

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <CustomDialogContent className="sm:max-w-[800px] w-[calc(100%-32px)] max-w-[95vw] max-h-[90vh] flex flex-col bg-white p-0 overflow-hidden">
          <DialogHeader className="border-b border-gray-300 pb-4 bg-white flex-shrink-0 px-6 pt-6 relative">
            <DialogTitle className="text-xl flex items-center gap-2 text-black pr-10">
              <User className="h-6 w-6 text-black" />
              Customer Details - {customer.id}
            </DialogTitle>
            {/* Custom close button to ensure proper positioning */}
            <button
              onClick={() => onOpenChange(false)}
              className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none p-1 hover:bg-gray-100"
            >
              <X className="h-4 w-4 text-black" />
              <span className="sr-only">Close</span>
            </button>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto">
            <div className="space-y-6 py-4 px-6">

              {/* Customer Information - First */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold flex items-center gap-2 text-black">
                  <User className="h-5 w-5 text-black" />
                  Customer Information
                </h3>
                <div className="border border-gray-300 rounded-lg p-4 bg-white">
                  <div className="space-y-4">
                    <div className="flex items-center gap-3 pb-3 border-b border-gray-200">
                      <Avatar className="h-12 w-12 border border-gray-300">
                        <AvatarImage src={customer.avatar} />
                        <AvatarFallback className="bg-gray-100 text-black text-lg">
                          {customer.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-semibold text-lg text-black">{customer.name}</p>
                        <p className="text-sm text-gray-600">Customer ID: {customer.id}</p>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <Mail size={16} className="text-gray-600" />
                        <span className="text-sm text-black">{customer.email}</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <Phone size={16} className="text-gray-600" />
                        <span className="text-sm text-black">{customer.phone}</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <MapPin size={16} className="text-gray-600" />
                        <span className="text-sm text-black">{customer.location}</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <Calendar size={16} className="text-gray-600" />
                        <span className="text-sm text-black">
                          Joined: {new Date(customer.joinDate).toLocaleDateString()}
                        </span>
                      </div>
                      {customer.address && (
                        <div className="flex items-center gap-3">
                          <MapPin size={16} className="text-gray-600" />
                          <span className="text-sm text-black">{customer.address}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Order History Section - Second */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold flex items-center gap-2 text-black">
                  <ShoppingBag className="h-5 w-5 text-black" />
                  Order History ({customer.orderHistory?.length || 0})
                </h3>
                <div className="border border-gray-300 rounded-lg overflow-hidden bg-white">
                  <div className="space-y-0">
                    {customer.orderHistory && customer.orderHistory.length > 0 ? (
                      customer.orderHistory.slice(0, 5).map((order, index) => (
                        <div
                          key={order.id}
                          className={`flex items-center gap-4 p-4 hover:bg-gray-50 cursor-pointer transition-colors ${index !== Math.min(customer.orderHistory!.length, 5) - 1 ? 'border-b border-gray-200' : ''}`}
                          onClick={() => setSelectedOrderId(selectedOrderId === order.id ? null : order.id)}
                        >
                          <div className="flex items-center gap-3">
                            <Package size={16} className="text-gray-600" />
                            <Eye size={14} className="text-blue-500" />
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <h4 className="font-medium text-black">Order #{order.id}</h4>
                              <Badge className={`${getOrderStatusColor(order.status)} border-0 px-2 py-1 text-xs font-medium flex items-center gap-1`}>
                                {getOrderStatusIcon(order.status)}
                                {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                              </Badge>
                            </div>
                            <p className="text-sm text-gray-600">
                              {new Date(order.orderDate).toLocaleDateString()} • {order.items} items • {order.paymentMethod}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="font-medium text-black">{formatCurrency(order.total)}</p>
                            <p className="text-sm text-gray-600">Click to view details</p>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="p-8 text-center text-gray-500">
                        <ShoppingBag className="h-12 w-12 mx-auto mb-3 text-gray-300" />
                        <p>No order history found</p>
                      </div>
                    )}
                  </div>

                  {/* Order Details Expansion */}
                  {selectedOrderId && customer.orderHistory && (
                    <div className="border-t border-gray-200 bg-gray-50">
                      {customer.orderHistory
                        .filter(order => order.id === selectedOrderId)
                        .map(order => (
                          <div key={order.id} className="p-4 space-y-4">
                            <h4 className="font-semibold text-black">Order Items:</h4>
                            <div className="space-y-2">
                              {order.orderItems.map(item => (
                                <div key={item.id} className="flex items-center gap-3 p-2 bg-white rounded border">
                                  <img
                                    src={item.productImage}
                                    alt={item.productName}
                                    className="w-10 h-10 object-cover rounded border"
                                  />
                                  <div className="flex-1">
                                    <p className="font-medium text-sm text-black">{item.productName}</p>
                                    <p className="text-xs text-gray-600">Qty: {item.quantity} × {formatCurrency(item.price)}</p>
                                  </div>
                                  <p className="font-medium text-sm text-black">{formatCurrency(item.total)}</p>
                                </div>
                              ))}
                            </div>
                            {order.notes && (
                              <div>
                                <p className="font-medium text-sm text-black">Notes:</p>
                                <p className="text-sm text-gray-600">{order.notes}</p>
                              </div>
                            )}
                          </div>
                        ))}
                    </div>
                  )}
                </div>
              </div>

              {/* Affiliate Information Section - Third */}
              {customer.affiliateWorker && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold flex items-center gap-2 text-black">
                    <Star className="h-5 w-5 text-black" />
                    Affiliate Information
                  </h3>
                  <div className="border border-gray-300 rounded-lg p-4 bg-white">
                    <div className="space-y-4">
                      <div className="flex items-center gap-3 pb-3 border-b border-gray-200">
                        <Avatar className="h-10 w-10 border border-gray-300">
                          <AvatarFallback className="bg-gray-100 text-black">
                            {customer.affiliateWorker.name.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium text-black">{customer.affiliateWorker.name}</p>
                          <p className="text-sm text-gray-600">
                            {customer.affiliateWorker.commissionRate}% Commission Rate
                          </p>
                        </div>
                      </div>

                      <div className="space-y-3">
                        <div className="flex items-center gap-3">
                          <Mail size={16} className="text-gray-600" />
                          <span className="text-sm text-black">{customer.affiliateWorker.email}</span>
                        </div>
                        <div className="flex items-center gap-3">
                          <Phone size={16} className="text-gray-600" />
                          <span className="text-sm text-black">{customer.affiliateWorker.phone}</span>
                        </div>
                        <div className="flex items-center gap-3">
                          <DollarSign size={16} className="text-gray-600" />
                          <span className="text-sm text-black">
                            Total Earnings: {formatCurrency(customer.affiliateWorker.totalEarnings)}
                          </span>
                        </div>
                        <div className="flex items-center gap-3">
                          <Calendar size={16} className="text-gray-600" />
                          <span className="text-sm text-black">
                            Joined: {new Date(customer.affiliateWorker.joinDate).toLocaleDateString()}
                          </span>
                        </div>
                        {customer.totalCommissionEarned && (
                          <div className="flex items-center gap-3">
                            <TrendingUp size={16} className="text-gray-600" />
                            <span className="text-sm text-black">
                              Commission from this customer: {formatCurrency(customer.totalCommissionEarned)}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Payment History Section - Fourth */}
              {customer.paymentHistory && customer.paymentHistory.length > 0 && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold flex items-center gap-2 text-black">
                    <CreditCard className="h-5 w-5 text-black" />
                    Payment History ({customer.paymentHistory.length})
                  </h3>
                  <div className="border border-gray-300 rounded-lg overflow-hidden bg-white">
                    <div className="space-y-0">
                      {customer.paymentHistory.slice(0, 5).map((payment, index) => (
                        <div key={payment.id} className={`flex items-center gap-4 p-4 ${index !== Math.min(customer.paymentHistory!.length, 5) - 1 ? 'border-b border-gray-200' : ''}`}>
                          <div className="flex items-center gap-3">
                            <CreditCard size={16} className="text-gray-600" />
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <h4 className="font-medium text-black">{payment.description}</h4>
                              <Badge className={`${getPaymentStatusColor(payment.status)} border-0 px-2 py-1 text-xs font-medium`}>
                                {payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}
                              </Badge>
                            </div>
                            <p className="text-sm text-gray-600">
                              {new Date(payment.date).toLocaleDateString()} • {payment.paymentMethod}
                              {payment.orderId && ` • Order #${payment.orderId}`}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="font-medium text-black">{formatCurrency(payment.amount)}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* Customer Summary with Status - Fifth */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold flex items-center gap-2 text-black">
                  <TrendingUp className="h-5 w-5 text-black" />
                  Customer Summary
                </h3>
                <div className="p-4 border border-gray-300 rounded-lg space-y-4 bg-white">
                  {/* Status Section */}
                  <div className="border-b border-gray-200 pb-4">
                    <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
                      <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium text-black">Status:</span>
                          <Badge className={`${getStatusColor(selectedStatus)} border-0 px-3 py-1.5 text-sm font-medium`}>
                            {selectedStatus.charAt(0).toUpperCase() + selectedStatus.slice(1)}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <Calendar className="h-4 w-4" />
                          <span>Member since {new Date(customer.joinDate).toLocaleDateString()}</span>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium text-black">Update Status:</span>
                        <Select
                          value={selectedStatus}
                          onValueChange={handleStatusChange}
                          disabled={isUpdatingStatus}
                        >
                          <SelectTrigger className="w-40 border-gray-300">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent className="bg-white border border-gray-300">
                            <SelectItem value="active">Active</SelectItem>
                            <SelectItem value="inactive">Inactive</SelectItem>
                            <SelectItem value="blocked">Blocked</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>

                  {/* Customer Statistics */}
                  <div className="space-y-3">
                    <div className="flex justify-between text-black">
                      <span>Total Orders:</span>
                      <span className="font-medium">{customer.totalOrders}</span>
                    </div>
                    <div className="flex justify-between text-black">
                      <span>Total Spent:</span>
                      <span className="font-medium">{formatCurrency(customer.totalSpent)}</span>
                    </div>
                    <div className="flex justify-between text-black">
                      <span>Average Order Value:</span>
                      <span className="font-medium">{formatCurrency(averageOrderValue)}</span>
                    </div>
                    <div className="border-t border-gray-200 pt-3">
                      <div className="flex justify-between text-black">
                        <span>Last Order:</span>
                        <span className="font-medium">
                          {customer.lastOrderDate
                            ? new Date(customer.lastOrderDate).toLocaleDateString()
                            : customer.orderHistory && customer.orderHistory.length > 0
                              ? new Date(customer.orderHistory[0].orderDate).toLocaleDateString()
                              : 'No orders yet'
                          }
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <ShoppingBag size={16} />
                      <span>Customer Lifetime Value: {formatCurrency(customer.totalSpent)}</span>
                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>

          {/* Fixed Bottom Action Buttons - Always Visible */}
          <div className="border-t border-gray-300 bg-white p-4 flex items-center justify-end gap-2 flex-shrink-0">
            <Button
              onClick={handleCancelStatusChange}
              disabled={!hasStatusChanged || isUpdatingStatus}
              variant="outline"
              size="sm"
              className={`border-gray-300 text-black ${
                !hasStatusChanged || isUpdatingStatus
                  ? 'opacity-50 cursor-not-allowed hover:bg-white'
                  : 'hover:bg-gray-100'
              }`}
            >
              <X className="h-4 w-4 mr-1" />
              Cancel
            </Button>
            <Button
              onClick={handleConfirmClick}
              disabled={!hasStatusChanged || isUpdatingStatus}
              size="sm"
              className={`border-black ${
                !hasStatusChanged || isUpdatingStatus
                  ? 'bg-gray-400 hover:bg-gray-400 text-gray-200 cursor-not-allowed border-gray-400'
                  : 'bg-black hover:bg-gray-800 text-white'
              }`}
            >
              {isUpdatingStatus ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              ) : (
                <Save className="h-4 w-4 mr-1" />
              )}
              Confirm Status Change
            </Button>
          </div>
        </CustomDialogContent>
      </Dialog>

      {/* Confirmation Dialog */}
      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent className="bg-white border border-gray-300">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-black">Confirm Status Change</AlertDialogTitle>
            <AlertDialogDescription className="text-gray-600">
              Are you sure you want to change the customer status from <strong>{customer.status}</strong> to <strong>{selectedStatus}</strong>?
              <br />
              <br />
              <span className="text-sm">Customer ID: {customer.id}</span>
              <br />
              <span className="text-sm">Customer: {customer.name}</span>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              className="border-gray-300 text-black hover:bg-gray-100"
              onClick={() => setShowConfirmDialog(false)}
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmStatusUpdate}
              className="bg-black hover:bg-gray-800 text-white"
              disabled={isUpdatingStatus}
            >
              {isUpdatingStatus ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              ) : null}
              Confirm Change
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default CustomerDetailsDialog;
