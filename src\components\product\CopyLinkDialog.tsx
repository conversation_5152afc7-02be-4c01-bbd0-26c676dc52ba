import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON>alogTitle, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { Copy, Check, Share2, Smartphone } from "lucide-react";

interface CopyLinkDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  productId: string;
  productName: string;
}

const CopyLinkDialog = ({ open, onOpenChange, productId, productName }: CopyLinkDialogProps) => {
  const [copied, setCopied] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // In a real application, this would generate a unique affiliate link
  const affiliateLink = `https://example.com/products/${productId}?ref=user123`;

  // Check if device is mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 640);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  const copyToClipboard = () => {
    navigator.clipboard.writeText(affiliateLink);
    setCopied(true);
    toast.success("Affiliate link copied to clipboard!");

    // Reset the copied state after 2 seconds
    setTimeout(() => {
      setCopied(false);
    }, 2000);
  };

  const shareLink = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: productName,
          text: `Check out this product: ${productName}`,
          url: affiliateLink,
        });
        toast.success("Link shared successfully!");
      } catch (error) {
        console.error("Error sharing:", error);
      }
    } else {
      copyToClipboard();
      toast.info("Sharing not supported on this device. Link copied instead!");
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md w-[calc(100%-32px)] max-w-[95vw]">
        <DialogHeader className="text-center sm:text-left">
          <DialogTitle className="text-xl">Your Affiliate Link</DialogTitle>
          <DialogDescription>
            Share this link with potential buyers to earn commission
          </DialogDescription>
        </DialogHeader>

        <div className="flex flex-col sm:flex-row sm:items-center gap-2 mt-4">
          <div className="grid flex-1 gap-2">
            <Input
              value={affiliateLink}
              readOnly
              className="font-mono text-xs sm:text-sm w-full"
              onClick={(e) => (e.target as HTMLInputElement).select()}
            />
            <p className="text-xs text-muted-foreground">
              You'll earn commission when someone purchases through this link
            </p>
          </div>

          {!isMobile && (
            <Button
              size="icon"
              variant={copied ? "default" : "outline"}
              onClick={copyToClipboard}
              className={copied ? "bg-green-600 hover:bg-green-700 flex-shrink-0" : "flex-shrink-0"}
            >
              {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
            </Button>
          )}
        </div>

        <div className="flex flex-col sm:flex-row sm:justify-between gap-2 mt-6">
          {isMobile && (
            <Button
              variant="outline"
              className="w-full"
              onClick={copyToClipboard}
            >
              {copied ? <Check className="h-4 w-4 mr-2" /> : <Copy className="h-4 w-4 mr-2" />}
              {copied ? "Copied!" : "Copy Link"}
            </Button>
          )}

          <div className="flex flex-col sm:flex-row gap-2 w-full">
            <Button
              onClick={shareLink}
              className="gap-2 w-full"
            >
              {navigator.share ? (
                <>
                  <Share2 className="h-4 w-4" />
                  Share Link
                </>
              ) : (
                <>
                  <Smartphone className="h-4 w-4" />
                  Send to Phone
                </>
              )}
            </Button>

            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="w-full"
            >
              Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CopyLinkDialog;
