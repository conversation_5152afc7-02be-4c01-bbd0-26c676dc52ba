import { useState, useEffect } from 'react';
import Layout from '@/components/layout/Layout';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { formatCurrency, generateRandomData } from '@/lib/utils';
import { DollarSign, Download, ArrowUpRight } from 'lucide-react';
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { toast } from 'sonner';
import StatsCard from '@/components/dashboard/StatsCard';
import { useIsMobile } from '@/hooks/use-mobile';

const Earnings = () => {
  const [dashboardData] = useState(generateRandomData());
  const [timeframe, setTimeframe] = useState("30days");
  const isMobile = useIsMobile();

  // Generate some example earnings data
  const generateEarningsData = (days: number) => {
    const data = [];
    const date = new Date();

    for (let i = days; i >= 0; i--) {
      const current = new Date(date);
      current.setDate(date.getDate() - i);

      // Format date differently based on device size and timeframe
      let dateFormat = { month: 'short', day: 'numeric' };

      // For mobile with longer timeframes, use more compact date format
      if (isMobile && days > 30) {
        // Only show every 5th day for 90 days on mobile
        if (i % 5 !== 0 && i !== 0 && i !== days) {
          continue;
        }
      }

      data.push({
        name: current.toLocaleDateString('en-US', dateFormat as Intl.DateTimeFormatOptions),
        amount: Math.random() * 100 + 10,
      });
    }

    return data;
  };

  // Regenerate data when mobile state changes
  const [earningsData, setEarningsData] = useState({
    "7days": generateEarningsData(7),
    "30days": generateEarningsData(30),
    "90days": generateEarningsData(90),
  });

  // Update data when mobile state changes
  useEffect(() => {
    setEarningsData({
      "7days": generateEarningsData(7),
      "30days": generateEarningsData(30),
      "90days": generateEarningsData(90),
    });
  }, [isMobile]);

  const downloadReport = (format: string) => {
    toast.success(`Downloaded earnings report as ${format.toUpperCase()}`);
  };

  return (
    <Layout>
      <div className="mb-4 sm:mb-6">
        <h1 className="text-2xl sm:text-3xl font-bold mb-1">Earnings</h1>
        <p className="text-sm sm:text-base text-muted-foreground">
          Track your affiliate commission earnings
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <StatsCard
          icon={<DollarSign />}
          title="Total Earnings"
          value={formatCurrency(dashboardData.totalCommission)}
          iconClassName="bg-primary/10 text-primary"
        />

        <StatsCard
          icon={<DollarSign />}
          title="This Month"
          value={formatCurrency(dashboardData.totalCommission / 3)}
          iconClassName="bg-success/10 text-success"
        />

        <StatsCard
          icon={<ArrowUpRight />}
          title="Growth"
          value={`+${dashboardData.commissionChange}%`}
          iconClassName="bg-accent/10 text-accent"
        />
      </div>

      <Card className="p-4 sm:p-6 mb-6 sm:mb-8">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 sm:mb-6">
          <h2 className="text-lg sm:text-xl font-semibold mb-2 sm:mb-0">Earnings Over Time</h2>
          <div className="flex items-center gap-2 w-full sm:w-auto">
            <Select value={timeframe} onValueChange={setTimeframe}>
              <SelectTrigger className="w-full sm:w-[180px] text-sm">
                <SelectValue placeholder="Select timeframe" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7days">Last 7 Days</SelectItem>
                <SelectItem value="30days">Last 30 Days</SelectItem>
                <SelectItem value="90days">Last 90 Days</SelectItem>
              </SelectContent>
            </Select>

            <Button variant="outline" size="icon" className="h-9 w-9" onClick={() => downloadReport('csv')}>
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="h-[250px] sm:h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart
              data={earningsData[timeframe as keyof typeof earningsData]}
              margin={isMobile
                ? { top: 10, right: 10, left: -20, bottom: 0 }
                : { top: 10, right: 30, left: 0, bottom: 0 }
              }
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey="name"
                tick={{ fontSize: isMobile ? 10 : 12 }}
                tickMargin={isMobile ? 5 : 10}
                interval={isMobile ? 'preserveStartEnd' : 0}
              />
              <YAxis
                tick={{ fontSize: isMobile ? 10 : 12 }}
                tickFormatter={(value) => isMobile ? `$${value}` : `$${value}`}
                width={isMobile ? 40 : 60}
              />
              <Tooltip
                formatter={(value) => [`$${Number(value).toFixed(2)}`, 'Amount']}
                contentStyle={{ fontSize: isMobile ? '10px' : '12px' }}
                itemStyle={{ fontSize: isMobile ? '10px' : '12px' }}
                labelStyle={{ fontSize: isMobile ? '10px' : '12px' }}
              />
              <Area
                type="monotone"
                dataKey="amount"
                stroke="#8B5CF6"
                fill="#8B5CF680"
                strokeWidth={isMobile ? 1.5 : 2}
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>
      </Card>

      <Card className="p-4 sm:p-6 border-t-4 border-t-primary">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 sm:mb-6">
          <div>
            <h2 className="text-lg sm:text-xl font-semibold mb-1">Earnings Breakdown</h2>
            <p className="text-xs sm:text-sm text-muted-foreground">Detailed analysis of your commission earnings</p>
          </div>
          <div className="flex items-center gap-2 w-full sm:w-auto mt-2 sm:mt-0">
            <Select defaultValue="thisMonth">
              <SelectTrigger className="w-full sm:w-[180px] text-xs sm:text-sm h-8 sm:h-9">
                <SelectValue placeholder="Select period" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="thisWeek">This Week</SelectItem>
                <SelectItem value="thisMonth">This Month</SelectItem>
                <SelectItem value="lastMonth">Last Month</SelectItem>
                <SelectItem value="last3Months">Last 3 Months</SelectItem>
                <SelectItem value="thisYear">This Year</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" className="w-auto text-xs sm:text-sm h-8 sm:h-9" onClick={() => downloadReport('excel')}>
              <Download className="h-3.5 w-3.5 mr-1 sm:mr-2" />
              Export
            </Button>
          </div>
        </div>

        <Tabs defaultValue="product">
          <TabsList className="mb-4 sm:mb-6 w-full bg-muted/50">
            <TabsTrigger value="product" className="flex-1 text-xs sm:text-sm">By Product</TabsTrigger>
            <TabsTrigger value="date" className="flex-1 text-xs sm:text-sm">By Date</TabsTrigger>
            <TabsTrigger value="category" className="flex-1 text-xs sm:text-sm">By Category</TabsTrigger>
          </TabsList>

          <TabsContent value="product">
            <div className="overflow-x-auto -mx-4 sm:mx-0">
              <Table className="w-full text-xs sm:text-sm">
                <TableHeader>
                  <TableRow className="bg-muted/30 hover:bg-muted/30">
                    <TableHead className="whitespace-nowrap font-semibold">Product</TableHead>
                    <TableHead className="whitespace-nowrap font-semibold text-center">Sales</TableHead>
                    <TableHead className="whitespace-nowrap font-semibold text-right">Revenue</TableHead>
                    <TableHead className="whitespace-nowrap font-semibold text-right">Commission</TableHead>
                    <TableHead className="whitespace-nowrap font-semibold text-right">Trend</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {[...Array(5)].map((_, i) => {
                    const sales = Math.floor(Math.random() * 20) + 5;
                    const revenue = Math.random() * 1000 + 500;
                    const commission = revenue * (Math.random() * 0.15 + 0.05);
                    const trend = Math.random() > 0.5 ? 'up' : 'down';
                    const trendValue = Math.floor(Math.random() * 30) + 1;

                    return (
                      <TableRow key={i} className="hover:bg-muted/10">
                        <TableCell className="font-medium">
                          <div className="flex items-center gap-2">
                            <div className="w-8 h-8 rounded-md bg-muted/20 flex items-center justify-center">
                              <img
                                src={`https://picsum.photos/seed/${i+10}/40/40`}
                                alt="Product"
                                className="w-6 h-6 object-contain rounded-sm"
                              />
                            </div>
                            <span>Premium Headphones {i+1}</span>
                          </div>
                        </TableCell>
                        <TableCell className="text-center">{sales}</TableCell>
                        <TableCell className="text-right">${revenue.toFixed(2)}</TableCell>
                        <TableCell className="text-right">
                          <span className="font-medium text-emerald-600 dark:text-emerald-500">
                            ${commission.toFixed(2)}
                          </span>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end">
                            <Badge variant={trend === 'up' ? 'success' : 'destructive'} className="text-xs px-1.5 py-0">
                              {trend === 'up' ? '+' : '-'}{trendValue}%
                              {trend === 'up' ?
                                <ArrowUpRight className="ml-0.5 h-3 w-3" /> :
                                <ArrowUpRight className="ml-0.5 h-3 w-3 rotate-180" />
                              }
                            </Badge>
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          </TabsContent>

          <TabsContent value="date">
            <div className="overflow-x-auto -mx-4 sm:mx-0">
              <Table className="w-full text-xs sm:text-sm">
                <TableHeader>
                  <TableRow className="bg-muted/30 hover:bg-muted/30">
                    <TableHead className="whitespace-nowrap font-semibold">Date</TableHead>
                    <TableHead className="whitespace-nowrap font-semibold text-center">Orders</TableHead>
                    <TableHead className="whitespace-nowrap font-semibold text-right">Revenue</TableHead>
                    <TableHead className="whitespace-nowrap font-semibold text-right">Commission</TableHead>
                    <TableHead className="whitespace-nowrap font-semibold text-right">Change</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {[...Array(5)].map((_, i) => {
                    const date = new Date();
                    date.setDate(date.getDate() - i);
                    const orders = Math.floor(Math.random() * 10) + 1;
                    const revenue = Math.random() * 500 + 100;
                    const commission = revenue * (Math.random() * 0.15 + 0.05);
                    const change = Math.random() > 0.5 ? 'up' : 'down';
                    const changeValue = Math.floor(Math.random() * 20) + 1;

                    return (
                      <TableRow key={i} className="hover:bg-muted/10">
                        <TableCell className="font-medium whitespace-nowrap">
                          {isMobile
                            ? date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
                            : date.toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' })
                          }
                        </TableCell>
                        <TableCell className="text-center">{orders}</TableCell>
                        <TableCell className="text-right">${revenue.toFixed(2)}</TableCell>
                        <TableCell className="text-right">
                          <span className="font-medium text-emerald-600 dark:text-emerald-500">
                            ${commission.toFixed(2)}
                          </span>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end">
                            <Badge variant={change === 'up' ? 'success' : 'destructive'} className="text-xs px-1.5 py-0">
                              {change === 'up' ? '+' : '-'}{changeValue}%
                              {change === 'up' ?
                                <ArrowUpRight className="ml-0.5 h-3 w-3" /> :
                                <ArrowUpRight className="ml-0.5 h-3 w-3 rotate-180" />
                              }
                            </Badge>
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          </TabsContent>

          <TabsContent value="category">
            <div className="overflow-x-auto -mx-4 sm:mx-0">
              <Table className="w-full text-xs sm:text-sm">
                <TableHeader>
                  <TableRow className="bg-muted/30 hover:bg-muted/30">
                    <TableHead className="whitespace-nowrap font-semibold">Category</TableHead>
                    <TableHead className="whitespace-nowrap font-semibold text-center">Products</TableHead>
                    <TableHead className="whitespace-nowrap font-semibold text-right">Revenue</TableHead>
                    <TableHead className="whitespace-nowrap font-semibold text-right">Commission</TableHead>
                    <TableHead className="whitespace-nowrap font-semibold text-right">% of Total</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {['Electronics', 'Clothing', 'Jewelry', 'Home Decor', 'Beauty'].map((category, i) => {
                    const products = Math.floor(Math.random() * 15) + 3;
                    const revenue = Math.random() * 2000 + 500;
                    const commission = revenue * (Math.random() * 0.15 + 0.05);
                    const percentage = Math.floor(Math.random() * 30) + 5;

                    return (
                      <TableRow key={i} className="hover:bg-muted/10">
                        <TableCell className="font-medium">{category}</TableCell>
                        <TableCell className="text-center">{products}</TableCell>
                        <TableCell className="text-right">${revenue.toFixed(2)}</TableCell>
                        <TableCell className="text-right">
                          <span className="font-medium text-emerald-600 dark:text-emerald-500">
                            ${commission.toFixed(2)}
                          </span>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end gap-2">
                            <div className="w-16 h-2 bg-muted rounded-full overflow-hidden">
                              <div
                                className="h-full bg-primary"
                                style={{ width: `${percentage}%` }}
                              ></div>
                            </div>
                            <span>{percentage}%</span>
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          </TabsContent>
        </Tabs>
      </Card>
    </Layout>
  );
};

export default Earnings;
