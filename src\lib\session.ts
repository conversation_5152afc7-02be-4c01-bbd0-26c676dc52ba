// Session management utilities for localStorage operations
import type { UserSession, UserProfile } from '../types';

const SESSION_KEY = 'whary_admin_session';

// Format date for display
export function formatJoinDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

// Get user initials for avatar
export function getUserInitials(name: string): string {
  return name
    .split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .slice(0, 2)
    .join('');
}

// Save user session to localStorage
export function saveUserSession(user: { id: string; email: string }, profile: UserProfile): void {
  try {
    const session: UserSession = {
      user: {
        id: user.id,
        email: user.email
      },
      profile,
      joinDate: formatJoinDate(profile.created_at)
    };
    
    localStorage.setItem(SESSION_KEY, JSON.stringify(session));
  } catch (error) {
    console.error('Failed to save user session:', error);
  }
}

// Get user session from localStorage
export function getUserSession(): UserSession | null {
  try {
    const sessionData = localStorage.getItem(SESSION_KEY);
    if (!sessionData) return null;
    
    const session = JSON.parse(sessionData) as UserSession;
    
    // Validate session structure
    if (!session.user?.id || !session.profile?.id) {
      console.warn('Invalid session data found, clearing session');
      clearUserSession();
      return null;
    }
    
    return session;
  } catch (error) {
    console.error('Failed to get user session:', error);
    clearUserSession();
    return null;
  }
}

// Clear user session from localStorage
export function clearUserSession(): void {
  try {
    localStorage.removeItem(SESSION_KEY);
  } catch (error) {
    console.error('Failed to clear user session:', error);
  }
}

// Check if user is authenticated
export function isAuthenticated(): boolean {
  const session = getUserSession();
  return session !== null;
}

// Get current user profile
export function getCurrentUserProfile(): UserProfile | null {
  const session = getUserSession();
  return session?.profile || null;
}

// Update user profile in session
export function updateUserProfileInSession(updatedProfile: Partial<UserProfile>): void {
  try {
    const session = getUserSession();
    if (!session) return;

    const updatedSession: UserSession = {
      ...session,
      profile: {
        ...session.profile,
        ...updatedProfile
      }
    };

    localStorage.setItem(SESSION_KEY, JSON.stringify(updatedSession));
  } catch (error) {
    console.error('Failed to update user profile in session:', error);
  }
}

// Validate session data integrity
export function validateSession(): boolean {
  try {
    const session = getUserSession();
    if (!session) return false;

    // Check required fields
    const requiredFields = ['user.id', 'user.email', 'profile.id', 'profile.name', 'profile.email'];

    for (const field of requiredFields) {
      const keys = field.split('.');
      let value: any = session;

      for (const key of keys) {
        value = value?.[key];
        if (value === undefined || value === null) {
          console.warn(`Session validation failed: missing ${field}`);
          return false;
        }
      }
    }

    return true;
  } catch (error) {
    console.error('Session validation error:', error);
    return false;
  }
}

// Clean up invalid sessions
export function cleanupInvalidSession(): void {
  if (!validateSession()) {
    console.log('Cleaning up invalid session data');
    clearUserSession();
  }
}
