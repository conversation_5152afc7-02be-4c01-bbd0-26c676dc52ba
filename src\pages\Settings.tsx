import { useState } from 'react';
import Layout from '@/components/layout/Layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { toast } from 'sonner';
import {
  Bell,
  Copy,
  MessageCircle,
  Save,
  ExternalLink,
  Info,
  CheckCircle,
  ArrowRight,
  Smartphone,
  Play
} from 'lucide-react';

const Settings = () => {
  // User's Telegram Chat ID (this would normally come from backend/database)
  const [userChatId] = useState('123456789');
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false);

  const [notificationSettings, setNotificationSettings] = useState({
    telegramNotifications: true,
    pushNotifications: true,
    orderUpdates: true,
    paymentNotifications: true,
  });

  const handleNotificationChange = (key: string) => {
    setNotificationSettings(prev => ({
      ...prev,
      [key]: !prev[key as keyof typeof prev],
    }));
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('Copied to clipboard!');
  };

  const saveSettings = () => {
    toast.success('Notification settings saved successfully');
  };

  const openTelegramBot = () => {
    window.open('https://t.me/YourBotUsername', '_blank');
  };

  return (
    <Layout>
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-1">Notification Settings</h1>
        <p className="text-muted-foreground">
          Manage your notification preferences and Telegram bot integration
        </p>
      </div>


      {/* Telegram Bot Setup Card */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5" />
            Telegram Bot Setup
          </CardTitle>
          <CardDescription>
            Connect with our Telegram bot to receive instant notifications
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Your Chat ID</Label>
            <div className="flex gap-2">
              <Input
                value={userChatId}
                readOnly
                className="font-mono bg-muted"
              />
              <Button
                variant="outline"
                size="icon"
                onClick={() => copyToClipboard(userChatId)}
              >
                <Copy className="h-4 w-4" />
              </Button>
            </div>
            <p className="text-sm text-muted-foreground">
              Use this ID to activate notifications in our Telegram bot
            </p>
          </div>

          <div className="flex gap-2">
            <Dialog open={isVideoModalOpen} onOpenChange={setIsVideoModalOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" className="flex items-center gap-2">
                  <Play className="h-4 w-4" />
                  Watch Setup Tutorial
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl">
                <DialogHeader>
                  <DialogTitle>Telegram Bot Setup Tutorial</DialogTitle>
                  <DialogDescription>
                    Learn how to set up and use our Telegram bot for notifications
                  </DialogDescription>
                </DialogHeader>
                <div className="w-full">
                  <video
                    controls
                    className="w-full rounded-lg"
                    poster="https://via.placeholder.com/800x450/f3f4f6/6b7280?text=Telegram+Bot+Setup+Tutorial"
                  >
                    <source
                      src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4"
                      type="video/mp4"
                    />
                    Your browser does not support the video tag.
                  </video>
                </div>
              </DialogContent>
            </Dialog>

            <Button onClick={openTelegramBot} className="flex items-center gap-2">
              <ExternalLink className="h-4 w-4" />
              Open Telegram Bot
            </Button>
          </div>
        </CardContent>
      </Card>
      {/* Notification Preferences Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Notification Preferences
          </CardTitle>
          <CardDescription>
            Choose which notifications you want to receive via Telegram and browser
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Notification Methods */}
          <div className="space-y-6">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
              <div className="space-y-0.5">
                <Label>Telegram Notifications</Label>
                <p className="text-sm text-muted-foreground">
                  Receive notifications via our Telegram bot
                </p>
              </div>
              <Switch
                checked={notificationSettings.telegramNotifications}
                onCheckedChange={() => handleNotificationChange('telegramNotifications')}
              />
            </div>

            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
              <div className="space-y-0.5">
                <Label>Browser Push Notifications</Label>
                <p className="text-sm text-muted-foreground">
                  Receive notifications in your browser
                </p>
              </div>
              <Switch
                checked={notificationSettings.pushNotifications}
                onCheckedChange={() => handleNotificationChange('pushNotifications')}
              />
            </div>
          </div>

          {/* Notification Types */}
          <div className="pt-6 border-t">
            <h3 className="text-sm font-medium mb-4">Notification Types</h3>
            <div className="space-y-6">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                <div className="space-y-0.5">
                  <Label>New Orders</Label>
                  <p className="text-sm text-muted-foreground">
                    Get notified when new orders are placed
                  </p>
                </div>
                <Switch
                  checked={notificationSettings.orderUpdates}
                  onCheckedChange={() => handleNotificationChange('orderUpdates')}
                />
              </div>

              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                <div className="space-y-0.5">
                  <Label>Payment Notifications</Label>
                  <p className="text-sm text-muted-foreground">
                    Get notified about payment confirmations and issues
                  </p>
                </div>
                <Switch
                  checked={notificationSettings.paymentNotifications}
                  onCheckedChange={() => handleNotificationChange('paymentNotifications')}
                />
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Button onClick={saveSettings} className="flex items-center gap-2">
            <Save className="h-4 w-4" />
            Save Notification Settings
          </Button>
        </CardFooter>
      </Card>
    </Layout>
  );
};

export default Settings;
