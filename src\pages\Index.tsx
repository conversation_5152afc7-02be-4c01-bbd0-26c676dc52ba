
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Layout from '@/components/layout/Layout';
import StatsCard from '@/components/dashboard/StatsCard';
import ProductCard from '@/components/dashboard/ProductCard';
import BuyersList from '@/components/dashboard/BuyersList';
import EarningsChart from '@/components/dashboard/EarningsChart';
import {
  Users,
  DollarSign,
  MousePointer,
  Package2,
  ArrowRight
} from "lucide-react";
import { Button } from '@/components/ui/button';
import {
  formatCurrency,
  generateRandomData,
  generateMockBuyers
} from '@/lib/utils';
import { getEnhancedProducts } from '@/lib/api';
import { useIsMobile } from '@/hooks/use-mobile';
import RecentOrders from '@/components/dashboard/RecentOrders';

const Index = () => {
  const navigate = useNavigate();
  // In a real app, this would come from API calls
  const [dashboardData] = useState(generateRandomData());
  const [products, setProducts] = useState<any[]>([]);
  const [recentBuyers] = useState(generateMockBuyers());
  const [loading, setLoading] = useState(true);
  const isMobile = useIsMobile();
  const [recentOrders, setRecentOrders] = useState<Order[]>([]);

  // Fetch products from API to match products page
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        const apiProducts = await getEnhancedProducts();
        setProducts(apiProducts);
      } catch (error) {
        console.error('Error fetching products:', error);
        // Fallback to empty array if API fails
        setProducts([]);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);

  useEffect(() => {
    // This is mock data - replace with actual API call
    const mockOrders = [
      {
        id: 'ORD-001',
        customerName: 'John Doe',
        customerEmail: '<EMAIL>',
        orderDate: '2024-01-15',
        status: 'delivered',
        total: 299.99,
        items: 3,
        paymentMethod: 'Credit Card',
        shippingCost: 15.00,
        orderItems: [],
        affiliateWorker: {
          id: 'aff-001',
          name: 'Ahmed Benali',
          email: '<EMAIL>',
          phone: '+213 555 987 654',
          commissionRate: 12.5,
          totalEarnings: 2450.75,
          joinDate: '2023-06-15'
        }
      },
      {
        id: 'ORD-002',
        customerName: 'Jane Smith',
        customerEmail: '<EMAIL>',
        orderDate: '2024-01-14',
        status: 'shipped',
        total: 149.50,
        items: 2,
        paymentMethod: 'PayPal',
        shippingCost: 10.00,
        orderItems: [],
        affiliateWorker: {
          id: 'aff-002',
          name: 'Fatima Khelifi',
          email: '<EMAIL>',
          phone: '+213 555 876 543',
          commissionRate: 10.0,
          totalEarnings: 1875.25,
          joinDate: '2023-08-20'
        }
      }
    ];

    setRecentOrders(mockOrders);
  }, []);

  return (
    <Layout>
      {/* Welcome section */}
      <div className="mb-6 sm:mb-8">
        <h1 className="text-2xl sm:text-3xl font-bold mb-1">
          Store Dashboard 📊
        </h1>
        <p className="text-sm sm:text-base text-muted-foreground">
          Manage your store, track sales, and monitor inventory from here.
        </p>
      </div>

      {/* Stats cards */}
      <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8">
        <StatsCard
          icon={<Package2 size={20} />}
          title="Total Products"
          value={products.length}
          change={"+12%"}
          className="animate-delay-1"
        />
        <StatsCard
          icon={<DollarSign size={20} />}
          title="Total Revenue"
          value={formatCurrency(dashboardData.totalCommission)}
          change={dashboardData.commissionChange}
          className="animate-delay-2"
          iconClassName="bg-success/10 text-success"
        />
        <StatsCard
          icon={<Users size={20} />}
          title="Total Customers"
          value={dashboardData.totalBuyers}
          change={dashboardData.buyersChange}
          className="animate-delay-3"
          iconClassName="bg-accent/10 text-accent"
        />
        <StatsCard
          icon={<MousePointer size={20} />}
          title="Pending Orders"
          value={dashboardData.totalClicks}
          change={dashboardData.clicksChange}
          className="animate-delay-4"
          iconClassName="bg-warning/10 text-warning"
        />
      </div>

      {/* Earnings Chart */}
      <div className="mb-8">
        <EarningsChart />
      </div>

      {/* Products section */}
      <div className="mb-6 sm:mb-8">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-0 mb-4 sm:mb-6">
          <h2 className="text-lg sm:text-xl font-semibold">Recent Products</h2>
          <Button
            variant="outline"
            size="sm"
            className="w-full sm:w-auto text-xs sm:text-sm"
            onClick={() => navigate('/products')}
          >
            <ArrowRight className="mr-1 sm:mr-2 h-4 w-4" />
            View All
          </Button>
        </div>

        <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-5 md:gap-6">
          {loading ? (
            // Loading skeleton
            Array.from({ length: 4 }).map((_, index) => (
              <div key={index} className="animate-pulse">
                <div className="bg-gray-200 aspect-square rounded-lg mb-4"></div>
                <div className="bg-gray-200 h-4 rounded mb-2"></div>
                <div className="bg-gray-200 h-4 rounded w-3/4"></div>
              </div>
            ))
          ) : (
            products.slice(0, 4).map((product) => (
              <ProductCard
                key={product.id}
                product={{
                  ...product,
                  title: product.title, // API products use 'title' field
                }}
              />
            ))
          )}
        </div>
      </div>

      {/* Recent Orders */}
      <div className="mb-6 sm:mb-8">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-0 mb-4 sm:mb-6">
          <h2 className="text-lg sm:text-xl font-semibold">Recent Orders</h2>
          <Button
            variant="outline"
            size="sm"
            className="w-full sm:w-auto text-xs sm:text-sm"
            onClick={() => navigate('/orders')}
          >
            <ArrowRight className="mr-1 sm:mr-2 h-4 w-4" />
            View All Orders
          </Button>
        </div>

        <div className="overflow-x-auto -mx-4 sm:mx-0">
          <RecentOrders orders={recentOrders} />
        </div>
      </div>
    </Layout>
  );
};

export default Index;




