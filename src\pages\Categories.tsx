import { useState, useEffect } from 'react';
import Layout from '@/components/layout/Layout';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, ArrowRight, Package, Tag, Plus, Edit, Trash2, Upload, X, Image as ImageIcon, Loader2 } from "lucide-react";
import { useNavigate } from 'react-router-dom';
import { getEnhancedCategories, createCategory, updateCategory, deleteCategory } from '@/lib/api';
import { Badge } from "@/components/ui/badge";
import { TooltipProvider, Tooltip, TooltipTrigger, TooltipContent } from "@/components/ui/tooltip";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from 'sonner';
import type { Category } from '@/types';

const Categories = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [newCategory, setNewCategory] = useState({
    name: '',
    image: null as File | null
  });
  const [imagePreview, setImagePreview] = useState<string>('');
  const [editImagePreview, setEditImagePreview] = useState<string>('');
  const [hasChanges, setHasChanges] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [deletingCategoryId, setDeletingCategoryId] = useState<string | null>(null);

  // Fetch categories from API
  useEffect(() => {
    const fetchCategories = async () => {
      setLoading(true);
      setError(null);

      try {
        const categoriesData = await getEnhancedCategories();
        setCategories(categoriesData);
      } catch (err) {
        console.error('Error fetching categories:', err);
        setError('Failed to load categories. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  // Filter categories based on search query
  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Navigate to products page with category filter
  const navigateToCategory = (categoryId: string, categoryName: string) => {
    navigate(`/products?category=${categoryName.toLowerCase()}`);
  };

  // Calculate total products
  const totalProducts = categories.reduce((sum, category) => sum + (category.productCount || 0), 0);

  // Handle image file selection for new category
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setNewCategory({ ...newCategory, image: file });

      // Create preview URL
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle image file selection for edit category
  const handleEditImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setEditingCategory({ ...editingCategory, image: file });
      setHasChanges(true);

      // Create preview URL
      const reader = new FileReader();
      reader.onload = (e) => {
        setEditImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Remove main image for new category
  const removeMainImage = () => {
    setNewCategory({ ...newCategory, image: null });
    setImagePreview('');
  };

  // Remove main image for edit category
  const removeEditMainImage = () => {
    if (editingCategory) {
      setEditingCategory({ ...editingCategory, image: null });
      setEditImagePreview('');
      setHasChanges(true);
    }
  };

  // Add new category
  const handleAddCategory = async () => {
    if (!newCategory.name.trim()) {
      toast.error('Category name is required');
      return;
    }

    if (!newCategory.image) {
      toast.error('Category image is required');
      return;
    }

    setIsSubmitting(true);
    try {
      const createdCategory = await createCategory({
        name: newCategory.name,
        image: newCategory.image
      });

      // Refresh categories list
      const updatedCategories = await getEnhancedCategories();
      setCategories(updatedCategories);

      setNewCategory({ name: '', image: null });
      setImagePreview('');
      setIsAddDialogOpen(false);
      toast.success('Category added successfully');
    } catch (error) {
      console.error('Error adding category:', error);
      toast.error('Failed to add category. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Edit category
  const handleEditCategory = async () => {
    if (!editingCategory?.name.trim()) {
      toast.error('Category name is required');
      return;
    }

    setIsSubmitting(true);
    try {
      const updateData: any = {
        name: editingCategory.name
      };

      // Only include image in update if it has changed
      if (editingCategory.image instanceof File) {
        updateData.image = editingCategory.image;
      } else if (typeof editingCategory.image === 'string' && editingCategory.image !== editingCategory.image) {
        updateData.image = editingCategory.image;
      }

      await updateCategory(editingCategory.id, updateData);

      // Refresh categories list
      const updatedCategories = await getEnhancedCategories();
      setCategories(updatedCategories);

      setIsEditDialogOpen(false);
      setEditingCategory(null);
      setEditImagePreview('');
      setHasChanges(false);
      toast.success('Category updated successfully');
    } catch (error) {
      console.error('Error updating category:', error);
      toast.error('Failed to update category. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Delete category
  const handleDeleteCategory = async (categoryId: string) => {
    setDeleteLoading(true);
    setDeletingCategoryId(categoryId);

    try {
      await deleteCategory(categoryId);

      // Refresh categories list
      const updatedCategories = await getEnhancedCategories();
      setCategories(updatedCategories);

      toast.success('Category deleted successfully');
    } catch (error) {
      console.error('Error deleting category:', error);
      toast.error('Failed to delete category');
    } finally {
      setDeleteLoading(false);
      setDeletingCategoryId(null);
    }
  };

  // Handle edit dialog open
  const handleEditDialogOpen = (category: Category) => {
    setEditingCategory({ ...category });
    setEditImagePreview('');
    setHasChanges(false);
    setIsEditDialogOpen(true);
  };

  // Handle edit field changes
  const handleEditFieldChange = (field: string, value: string) => {
    setEditingCategory({ ...editingCategory, [field]: value });
    setHasChanges(true);
  };

  return (
    <Layout>
      <div className="mb-4 sm:mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold mb-1">Categories Management</h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              Manage product categories for your store
            </p>
          </div>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Category
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Add New Category</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="categoryName">Category Name *</Label>
                  <Input
                    id="categoryName"
                    value={newCategory.name}
                    onChange={(e) => setNewCategory({...newCategory, name: e.target.value})}
                    placeholder="Enter category name"
                    className="outline-none focus:outline-none focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0"
                  />
                </div>
                <div>
                  <Label htmlFor="categoryImage" className="flex items-center gap-2 text-base font-medium">
                    <ImageIcon className="h-4 w-4 text-primary" />
                    Category Image *
                  </Label>
                  <p className="text-sm text-muted-foreground mb-4">
                    Upload an image that will represent your category
                  </p>

                  {!imagePreview ? (
                    <div
                      className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-primary/50 transition-colors cursor-pointer"
                      onClick={() => document.getElementById('categoryImage')?.click()}
                    >
                      <div className="flex flex-col items-center space-y-4">
                        <div className="p-4 bg-primary/10 rounded-full">
                          <Upload className="h-8 w-8 text-primary" />
                        </div>
                        <div>
                          <p className="text-lg font-medium text-primary">
                            Click to upload category image
                          </p>
                          <p className="text-sm text-muted-foreground mt-1">
                            PNG, JPG, GIF up to 10MB
                          </p>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div className="relative w-full max-w-md mx-auto">
                        <div className="relative group">
                          <img
                            src={imagePreview}
                            alt="Category image"
                            className="w-full h-64 object-cover rounded-lg border-2 border-primary/30 shadow-md"
                          />
                          <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                            <Button
                              type="button"
                              variant="destructive"
                              size="sm"
                              onClick={removeMainImage}
                              className="bg-red-500 hover:bg-red-600"
                            >
                              <X className="h-4 w-4 mr-2" />
                              Remove
                            </Button>
                          </div>
                        </div>
                        <div className="absolute top-2 left-2 bg-primary text-primary-foreground px-2 py-1 rounded text-xs font-medium">
                          Category Image
                        </div>
                      </div>
                      <div className="text-center">
                        <button
                          type="button"
                          onClick={() => document.getElementById('categoryImageReplace')?.click()}
                          className="text-sm text-primary hover:text-primary/80 underline"
                        >
                          Change category image
                        </button>
                      </div>
                    </div>
                  )}

                  <Input
                    id="categoryImage"
                    type="file"
                    accept="image/*"
                    onChange={handleImageChange}
                    className="hidden"
                    required={!newCategory.image}
                  />

                  <Input
                    id="categoryImageReplace"
                    type="file"
                    accept="image/*"
                    onChange={handleImageChange}
                    className="hidden"
                  />
                </div>
                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => {
                    setIsAddDialogOpen(false);
                    setNewCategory({ name: '', image: null });
                    setImagePreview('');
                  }}>
                    Cancel
                  </Button>
                  <Button
                    onClick={handleAddCategory}
                    disabled={isSubmitting || !newCategory.name.trim() || !newCategory.image}
                  >
                    {isSubmitting ? 'Adding...' : 'Add Category'}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Summary cards */}
      {!loading && !error && categories.length > 0 && (
        <div className="grid grid-cols-2 gap-4 mb-6">
          <Card className="border shadow-sm">
            <CardContent className="p-4 flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Categories</p>
                <p className="text-2xl font-bold">{categories.length}</p>
              </div>
              <div className="p-3 bg-primary/10 rounded-full">
                <Tag size={20} className="text-primary" />
              </div>
            </CardContent>
          </Card>

          <Card className="border shadow-sm">
            <CardContent className="p-4 flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Products</p>
                <p className="text-2xl font-bold">{totalProducts}</p>
              </div>
              <div className="p-3 bg-primary/10 rounded-full">
                <Package size={20} className="text-primary" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Search bar */}
      <div className="relative max-w-md w-full mb-6">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground" size={18} />
        <Input
          placeholder="Search categories..."
          className="pl-10 w-full bg-muted/40"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>

      {/* Section title */}
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold">Categories</h2>
        <p className="text-sm text-muted-foreground">
          {filteredCategories.length} {filteredCategories.length === 1 ? 'category' : 'categories'} available
        </p>
      </div>

      {/* Categories grid */}
      {loading ? (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 sm:gap-5 md:gap-6">
          {[...Array(8)].map((_, i) => (
            <Card key={i} className="h-[320px] animate-pulse">
              <div className="h-full flex flex-col">
                <div className="aspect-square w-full bg-muted rounded-t-lg"></div>
                <div className="p-4 flex-1 flex flex-col gap-2">
                  <div className="h-4 bg-muted rounded w-3/4"></div>
                  <div className="h-4 bg-muted rounded w-1/2"></div>
                  <div className="mt-auto h-8 bg-muted rounded"></div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      ) : error ? (
        <div className="text-center py-12">
          <p className="text-red-500">{error}</p>
        </div>
      ) : filteredCategories.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-lg text-muted-foreground">No categories found matching your search.</p>
        </div>
      ) : (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 sm:gap-5 md:gap-6">
          <TooltipProvider>
            {filteredCategories.map((category) => (
              <Tooltip key={category.id}>
                <TooltipTrigger asChild>
                  <Card className="overflow-hidden transition-all duration-300 hover:shadow-lg group border h-full flex flex-col">
                    <div className="relative aspect-square w-full overflow-hidden">
                      {/* Category image */}
                      <div className="absolute inset-0 z-10 p-4">
                        <img
                          src={category.image}
                          alt={category.name}
                          className="w-full h-full object-cover rounded-lg transition-transform duration-700 group-hover:scale-105"
                          onError={(e) => {
                            e.currentTarget.src = `https://picsum.photos/seed/${category.name}/400/400`;
                          }}
                        />
                      </div>

                      {/* Admin actions */}
                      <div className="absolute top-2 right-2 z-20 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                        <Button
                          variant="secondary"
                          size="sm"
                          className="h-8 w-8 p-0"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEditDialogOpen(category);
                          }}
                        >
                          <Edit size={14} />
                        </Button>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button
                              variant="destructive"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={(e) => {
                                e.stopPropagation();
                              }}
                            >
                              <Trash2 size={14} />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent className="bg-white border border-gray-300">
                            <AlertDialogHeader>
                              <AlertDialogTitle className="text-black">Delete Category</AlertDialogTitle>
                              <AlertDialogDescription className="text-gray-600">
                                Are you sure you want to delete "{category.name}"? This action cannot be undone.
                                <br />
                                <br />
                                <span className="text-sm">Category ID: {category.id}</span>
                                <br />
                                <span className="text-sm">Products in category: {category.productCount}</span>
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel className="border-gray-300 text-black hover:bg-gray-100">
                                Cancel
                              </AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => handleDeleteCategory(category.id)}
                                className="bg-red-600 hover:bg-red-700 text-white"
                                disabled={deleteLoading && deletingCategoryId === category.id}
                              >
                                {deleteLoading && deletingCategoryId === category.id ? (
                                  <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Deleting...
                                  </>
                                ) : (
                                  'Delete Category'
                                )}
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>

                      {/* Category badge */}
                      
                    </div>

                    <CardContent className="p-4 flex-1">
                      <h3 className="font-bold text-base sm:text-lg mb-2">{category.name}</h3>
                      {category.description && (
                        <p className="text-sm text-muted-foreground mb-2 line-clamp-2">{category.description}</p>
                      )}
                      <div className="flex items-center gap-2 text-xs sm:text-sm text-muted-foreground">
                        <Package size={14} />
                        <span>{category.productCount} Products</span>
                      </div>
                    </CardContent>

                    <CardFooter className="p-3 border-t mt-auto">
                      <Button
                        variant="default"
                        size="sm"
                        className="w-full"
                        onClick={() => navigateToCategory(category.id, category.name)}
                      >
                        <span className="mr-1">View Products</span>
                        <ArrowRight size={14} />
                      </Button>
                    </CardFooter>
                  </Card>
                </TooltipTrigger>
                <TooltipContent side="top">
                  <p>View {category.productCount} products in {category.name}</p>
                </TooltipContent>
              </Tooltip>
            ))}
          </TooltipProvider>
        </div>
      )}

      {/* Edit Category Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-md max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader className="flex-shrink-0 border-b pb-2 pt-3">
            <DialogTitle>Edit Category</DialogTitle>
          </DialogHeader>

          <div
            className="flex-1 overflow-y-auto space-y-3 pt-1 pb-2"
            style={{
              scrollbarWidth: 'none',
              msOverflowStyle: 'none',
              WebkitScrollbar: { display: 'none' }
            }}
          >
            <style jsx>{`
              .flex-1::-webkit-scrollbar {
                display: none;
              }
            `}</style>

            <div>
              <Label htmlFor="editCategoryName">Category Name *</Label>
              <Input
                id="editCategoryName"
                value={editingCategory?.name || ''}
                onChange={(e) => handleEditFieldChange('name', e.target.value)}
                placeholder="Enter category name"
                className="outline-none focus:outline-none focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0"
              />
            </div>

            <div>
              <Label htmlFor="editCategoryImage" className="flex items-center gap-2 text-base font-medium">
                <ImageIcon className="h-4 w-4 text-primary" />
                Category Image *
              </Label>
              <p className="text-sm text-muted-foreground mb-4">
                Upload an image that will represent your category
              </p>

              {!(editImagePreview || editingCategory?.image) ? (
                <div
                  className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-primary/50 transition-colors cursor-pointer"
                  onClick={() => document.getElementById('editCategoryImage')?.click()}
                >
                  <div className="flex flex-col items-center space-y-4">
                    <div className="p-4 bg-primary/10 rounded-full">
                      <Upload className="h-8 w-8 text-primary" />
                    </div>
                    <div>
                      <p className="text-lg font-medium text-primary">
                        Click to upload category image
                      </p>
                      <p className="text-sm text-muted-foreground mt-1">
                        PNG, JPG, GIF up to 10MB
                      </p>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="relative w-full max-w-md mx-auto">
                    <div className="relative group">
                      <img
                        src={editImagePreview || editingCategory?.image}
                        alt="Category image"
                        className="w-full h-64 object-cover rounded-lg border-2 border-primary/30 shadow-md"
                      />
                      <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          onClick={removeEditMainImage}
                          className="bg-red-500 hover:bg-red-600"
                        >
                          <X className="h-4 w-4 mr-2" />
                          Remove
                        </Button>
                      </div>
                    </div>
                    <div className="absolute top-2 left-2 bg-primary text-primary-foreground px-2 py-1 rounded text-xs font-medium">
                      Category Image
                    </div>
                  </div>
                  <div className="text-center">
                    <button
                      type="button"
                      onClick={() => document.getElementById('editCategoryImageReplace')?.click()}
                      className="text-sm text-primary hover:text-primary/80 underline"
                    >
                      Change category image
                    </button>
                  </div>
                </div>
              )}

              <Input
                id="editCategoryImage"
                type="file"
                accept="image/*"
                onChange={handleEditImageChange}
                className="hidden"
              />

              <Input
                id="editCategoryImageReplace"
                type="file"
                accept="image/*"
                onChange={handleEditImageChange}
                className="hidden"
              />
            </div>
          </div>

          <div className="flex-shrink-0 border-t pt-4 flex justify-end gap-2">
            <Button
              variant="outline"
              onClick={() => {
                setIsEditDialogOpen(false);
                setEditingCategory(null);
                setEditImagePreview('');
                setHasChanges(false);
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleEditCategory}
              disabled={!hasChanges || isSubmitting || !editingCategory?.name.trim()}
            >
              {isSubmitting ? 'Updating...' : 'Update Category'}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </Layout>
  );
};

export default Categories;




