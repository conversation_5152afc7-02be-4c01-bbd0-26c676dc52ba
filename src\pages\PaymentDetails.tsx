import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import Layout from '@/components/layout/Layout';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  ArrowLeft,
  Calendar,
  DollarSign,
  CreditCard,
  Clock,
  CheckCircle,
  AlertCircle,
  User,
  FileText
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { formatCurrency } from '@/lib/utils';
import { fetchPaymentByIdWithUser, updatePaymentStatus } from '@/lib/api-extended';
import { Payment } from '@/types';

const PaymentDetails = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [payment, setPayment] = useState<Payment | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);

  useEffect(() => {
    const loadPayment = async () => {
      if (!id) return;
      
      try {
        setLoading(true);
        const paymentData = await fetchPaymentByIdWithUser(id);
        setPayment(paymentData);
      } catch (error) {
        console.error('Error fetching payment:', error);
        setPayment(null);
      } finally {
        setLoading(false);
      }
    };

    loadPayment();
  }, [id]);

  const handleMarkAsPaid = async () => {
    if (!payment || payment.status === 'paid') return;

    try {
      setUpdating(true);
      await updatePaymentStatus(payment.id, 'paid');
      setPayment(prev => prev ? { ...prev, status: 'paid' } : null);
      setShowConfirmDialog(false);
    } catch (error) {
      console.error('Error updating payment status:', error);
    } finally {
      setUpdating(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading payment details...</p>
        </div>
      </Layout>
    );
  }

  if (!payment) {
    return (
      <Layout>
        <div className="text-center py-8">
          <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Payment Not Found</h2>
          <p className="text-muted-foreground mb-4">The payment you're looking for doesn't exist.</p>
          <Button onClick={() => navigate('/payment')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Payment Management
          </Button>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="mb-4 sm:mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4 mb-4">
          <Button
            variant="outline"
            onClick={() => navigate('/payment')}
            className="flex items-center gap-2 w-fit"
          >
            <ArrowLeft size={16} />
            Back
          </Button>
          <div className="flex-1">
            <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold mb-1">Payment Details</h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              Payment ID: {payment.id}
            </p>
          </div>
        </div>
      </div>

      {/* Payment Info Card */}
      <Card className="mb-4 sm:mb-6">
        <CardHeader className="pb-3 sm:pb-6">
          <CardTitle className="text-lg sm:text-xl">Payment Information</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* User Information */}
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <Avatar className="h-12 w-12 flex-shrink-0">
                  <AvatarFallback className="text-lg">
                    {payment.user_name ? 
                      payment.user_name.split(' ').map(n => n[0]).join('') : 
                      'U'
                    }
                  </AvatarFallback>
                </Avatar>
                <div className="min-w-0 flex-1">
                  <h3 className="text-lg font-semibold">
                    {payment.user_name || 'Unknown User'}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    User ID: {payment.affiliate_worker_id}
                  </p>
                </div>
              </div>
            </div>

            {/* Payment Status */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Status:</span>
                <Badge className={getStatusColor(payment.status)}>
                  {payment.status}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Payment Date:</span>
                <span className="text-sm">
                  {new Date(payment.payment_date).toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Payment Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="min-w-0 flex-1">
                <p className="text-sm text-muted-foreground">Benefit Amount</p>
                <p className="text-2xl font-bold">{formatCurrency(payment.amount)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-green-500 flex-shrink-0 ml-2" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="min-w-0 flex-1">
                <p className="text-sm text-muted-foreground">Payment Method</p>
                <p className="text-lg font-medium">
                  {payment.payment_method || 'Not specified'}
                </p>
              </div>
              <CreditCard className="h-8 w-8 text-blue-500 flex-shrink-0 ml-2" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Notes Section */}
      {payment.notes && (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <FileText size={20} />
              Notes
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">{payment.notes}</p>
          </CardContent>
        </Card>
      )}

      {/* Actions */}
      {payment.status === 'pending' && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <Button
              onClick={() => setShowConfirmDialog(true)}
              disabled={updating}
              className="bg-green-600 hover:bg-green-700"
            >
              <CheckCircle size={16} className="mr-2" />
              {updating ? 'Updating...' : 'Mark as Paid'}
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Confirmation Dialog */}
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent className="bg-white w-[95vw] max-w-md mx-auto">
          <DialogHeader>
            <DialogTitle className="text-lg sm:text-xl">Confirm Payment</DialogTitle>
            <DialogDescription className="text-sm sm:text-base">
              Are you sure you want to mark this payment as paid?
            </DialogDescription>
          </DialogHeader>
          <div className="py-3 sm:py-4">
            <div className="bg-gray-50 p-3 sm:p-4 rounded-lg">
              <div className="flex justify-between items-center mb-2">
                <span className="font-medium text-sm sm:text-base">Amount:</span>
                <span className="text-base sm:text-lg font-bold">{formatCurrency(payment.amount)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="font-medium text-sm sm:text-base">User:</span>
                <span className="text-sm sm:text-base">{payment.user_name || 'Unknown User'}</span>
              </div>
            </div>
          </div>
          <DialogFooter className="flex flex-col sm:flex-row gap-2">
            <Button
              variant="outline"
              onClick={() => setShowConfirmDialog(false)}
              disabled={updating}
              className="w-full sm:w-auto"
            >
              Cancel
            </Button>
            <Button
              onClick={handleMarkAsPaid}
              disabled={updating}
              className="bg-green-600 hover:bg-green-700 w-full sm:w-auto"
            >
              {updating ? 'Processing...' : 'Confirm Payment'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Layout>
  );
};

export default PaymentDetails;
