
import { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import Layout from '@/components/layout/Layout';
import ProductCard from '@/components/dashboard/ProductCard';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, SlidersHorizontal, Tag, Plus, Grid3X3, List, Eye, Edit } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { getEnhancedProducts, getEnhancedProductsByCategory } from '@/lib/api';

const Products = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [products, setProducts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('default');
  const [categoryFilter, setCategoryFilter] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'table'>('grid');

  // Fetch products based on category filter
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        const params = new URLSearchParams(location.search);
        const category = params.get('category');

        if (category) {
          setCategoryFilter(category);
          const categoryProducts = await getEnhancedProductsByCategory(category);
          setProducts(categoryProducts);
        } else {
          const allProducts = await getEnhancedProducts();
          setProducts(allProducts);
        }
      } catch (err) {
        console.error('Error fetching products:', err);
        setError('Failed to load products. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [location.search]);

  // Filter products based on search term
  const filteredProducts = products.filter(product => {
    // First filter by search term
    const matchesSearch =
      product.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.category?.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesSearch;
  });

  // Sort products based on selected option
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    switch (sortBy) {
      case 'price-asc':
        return a.price - b.price;
      case 'price-desc':
        return b.price - a.price;
      case 'commission-desc':
        // Use affiliate_earning_price for sorting, fallback to commission calculation
        const aCommission = a.affiliate_earning_price || (a.commission ? (a.price * a.commission) / 100 : 0);
        const bCommission = b.affiliate_earning_price || (b.commission ? (b.price * b.commission) / 100 : 0);
        return bCommission - aCommission;
      case 'rating-high-low':
        return (b.rating?.rate || 0) - (a.rating?.rate || 0);
      default:
        return 0;
    }
  });

  return (
    <Layout>
      <div className="mb-4 sm:mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold mb-1">Products Management</h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              Manage your store products
            </p>
          </div>
          <Button onClick={() => navigate('/products/new')}>
            <Plus className="h-4 w-4 mr-2" />
            Add Product
          </Button>
        </div>

        {categoryFilter && (
          <div className="mt-2 flex items-center gap-2">
            <span className="text-sm">Filtered by:</span>
            <Badge className="flex items-center gap-1.5 px-3 py-1">
              <Tag size={14} />
              {categoryFilter.charAt(0).toUpperCase() + categoryFilter.slice(1)}
              <button
                className="ml-1 hover:text-primary"
                onClick={() => {
                  setCategoryFilter(null);
                  window.history.pushState({}, '', '/products');
                  window.location.reload();
                }}
                aria-label="Remove filter"
              >
                ×
              </button>
            </Badge>
          </div>
        )}
      </div>

      <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search products..."
            className="pl-10 text-sm sm:text-base"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="flex items-center gap-2">
          {/* View Toggle Buttons */}
          <div className="flex border rounded-md">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
              className="rounded-r-none"
            >
              <Grid3X3 className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'table' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('table')}
              className="rounded-l-none"
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <div className="flex items-center gap-2 w-full sm:w-auto sm:min-w-[200px]">
          <SlidersHorizontal className="h-4 w-4 text-muted-foreground" />
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="text-sm sm:text-base flex-1 sm:flex-none">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="default">Default</SelectItem>
              <SelectItem value="price-asc">Price: Low to High</SelectItem>
              <SelectItem value="price-desc">Price: High to Low</SelectItem>
              <SelectItem value="commission-desc">Highest Commission</SelectItem>
              <SelectItem value="rating-high-low">Highest Rating</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {loading ? (
        <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-5 md:gap-6">
          {[...Array(8)].map((_, i) => (
            <Card key={i} className="h-[300px] animate-pulse">
              <div className="h-full flex flex-col">
                <div className="h-[180px] bg-muted rounded-t-lg"></div>
                <div className="p-4 flex-1 flex flex-col gap-2">
                  <div className="h-4 bg-muted rounded w-3/4"></div>
                  <div className="h-4 bg-muted rounded w-1/2"></div>
                  <div className="mt-auto h-8 bg-muted rounded"></div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      ) : error ? (
        <div className="text-center py-12">
          <p className="text-red-500">{error}</p>
        </div>
      ) : sortedProducts.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-lg text-muted-foreground">No products found matching your search.</p>
        </div>
      ) : viewMode === 'grid' ? (
        <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-5 md:gap-6">
          {sortedProducts.map((product) => (
            <ProductCard key={product.id} product={product} />
          ))}
        </div>
      ) : (
        <Card>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Image</TableHead>
                  <TableHead>Product</TableHead>
                  <TableHead>Price</TableHead>
                  <TableHead>Commission</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Stock</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {sortedProducts.map((product) => {
                  // Use affiliate_earning_price directly, fallback to commission calculation for backward compatibility
                  const commissionAmount = product.affiliate_earning_price || (product.commission ? (product.price * product.commission) / 100 : 0);
                  // Get stock count from database field
                  const stockCount = product.stock_count ?? product.stockCount ?? 0;
                  return (
                    <TableRow key={product.id}>
                      <TableCell>
                        <div className="w-16 h-16 relative rounded-md overflow-hidden border border-gray-100">
                          <img
                            src={product.main_image || product.image || `https://picsum.photos/seed/${product.id}/600/400`}
                            alt={product.title || product.name}
                            className="object-cover w-full h-full"
                            onError={(e) => {
                              e.currentTarget.src = `https://picsum.photos/seed/${product.id}/600/400`;
                            }}
                          />
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">{product.title || product.name}</p>
                          <p className="text-xs text-muted-foreground line-clamp-1">{product.description}</p>
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">{Math.round(product.price)} Da</TableCell>
                      <TableCell className="text-emerald-600 font-medium">{Math.round(commissionAmount)} Da</TableCell>
                      <TableCell>
                        <Badge variant="outline" className="bg-secondary/70 text-secondary-foreground">
                          {product.category}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant="outline"
                          className={`${stockCount > 0 ? 'text-green-600' : 'text-red-500'}`}
                        >
                          {stockCount > 0 ? `${stockCount}` : "0"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => navigate(`/products/${product.id}`)}
                          >
                            <Eye size={16} />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              navigate(`/products/edit/${product.id}`);
                            }}
                          >
                            <Edit size={16} />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        </Card>
      )}
    </Layout>
  );
};

export default Products;
