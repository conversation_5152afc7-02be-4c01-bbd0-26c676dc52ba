
import { useState, useEffect } from 'react';
import Layout from '@/components/layout/Layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { toast } from 'sonner';
import { User } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { getUserInitials } from '@/lib/session';
import { useNavigate } from 'react-router-dom';

const Profile = () => {
  const { userProfile, user } = useAuth();
  const navigate = useNavigate();

  // Get user data with fallbacks
  const userData = {
    name: userProfile?.name || user?.email?.split('@')[0] || 'User',
    email: userProfile?.email || user?.email || '',
    phone: userProfile?.phone || '',
    joinDate: userProfile?.created_at ? new Date(userProfile.created_at).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }) : '',
    role: userProfile?.role || 'User',
  };

  const [formData, setFormData] = useState({
    name: userData.name,
    email: userData.email,
    phone: userData.phone,
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  // Update form data when user profile changes
  useEffect(() => {
    setFormData(prev => ({
      ...prev,
      name: userData.name,
      email: userData.email,
      phone: userData.phone,
    }));
  }, [userData.name, userData.email, userData.phone]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleProfileUpdate = (e: React.FormEvent) => {
    e.preventDefault();
    toast.success('Profile updated successfully!');
  };

  const handlePasswordUpdate = (e: React.FormEvent) => {
    e.preventDefault();

    if (formData.newPassword !== formData.confirmPassword) {
      toast.error('New passwords do not match');
      return;
    }

    if (formData.newPassword.length < 8) {
      toast.error('Password must be at least 8 characters');
      return;
    }

    toast.success('Password updated successfully!');

    // Reset password fields
    setFormData(prev => ({
      ...prev,
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    }));
  };

  return (
    <Layout>
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-1">My Profile</h1>
        <p className="text-muted-foreground">
          Manage your account information and settings
        </p>
      </div>

      <div className="mb-8">
        <Card className="border-t-4 border-t-primary overflow-hidden">
          <CardContent className="p-0">
            <div className="bg-gradient-to-r from-primary/10 to-transparent p-6 sm:p-8">
              <div className="flex flex-col md:flex-row gap-8 items-center md:items-start">
                <div className="flex flex-col items-center">
                  <Avatar className="w-32 h-32 mb-4 border-4 border-background shadow-lg">
                    <AvatarFallback className="text-xl font-bold bg-primary text-primary-foreground">
                      {userData.name ? getUserInitials(userData.name) : <User className="w-16 h-16" />}
                    </AvatarFallback>
                  </Avatar>

                  <div className="flex flex-col items-center mt-2">
                    <p className="text-sm font-medium text-primary mb-1 capitalize">{userData.role}</p>
                    <p className="text-sm text-muted-foreground">
                      {userData.joinDate ? `Member since ${userData.joinDate}` : 'New Member'}
                    </p>
                  </div>
                </div>

                <div className="flex-1 text-center md:text-left">
                  <h2 className="text-2xl font-bold mb-1">{userData.name}</h2>
                  <p className="text-muted-foreground mb-4">{userData.email}</p>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div className="bg-background/50 p-3 rounded-lg">
                      <h3 className="text-xs font-medium text-muted-foreground uppercase tracking-wider">Email</h3>
                      <p className="font-medium">{userData.email}</p>
                    </div>
                    <div className="bg-background/50 p-3 rounded-lg">
                      <h3 className="text-xs font-medium text-muted-foreground uppercase tracking-wider">Phone</h3>
                      <p className="font-medium">{userData.phone || 'Not provided'}</p>
                    </div>
                    <div className="bg-background/50 p-3 rounded-lg">
                      <h3 className="text-xs font-medium text-muted-foreground uppercase tracking-wider">Join Date</h3>
                      <p className="font-medium">{userData.joinDate}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>


          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="account" className="w-full">
        <TabsList className="mb-8">
          <TabsTrigger value="account">Account Settings</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
        </TabsList>

        <TabsContent value="account">
          <Card>
            <CardHeader>
              <CardTitle>Account Information</CardTitle>
              <CardDescription>Update your personal information</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleProfileUpdate} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Full Name</Label>
                    <Input
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleInputChange}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone">Phone Number</Label>
                  <Input
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                  />
                </div>

                <Button type="submit">Update Profile</Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security">
          <Card>
            <CardHeader>
              <CardTitle>Change Password</CardTitle>
              <CardDescription>Update your account password</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handlePasswordUpdate} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="currentPassword">Current Password</Label>
                  <Input
                    id="currentPassword"
                    name="currentPassword"
                    type="password"
                    value={formData.currentPassword}
                    onChange={handleInputChange}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="newPassword">New Password</Label>
                    <Input
                      id="newPassword"
                      name="newPassword"
                      type="password"
                      value={formData.newPassword}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword">Confirm Password</Label>
                    <Input
                      id="confirmPassword"
                      name="confirmPassword"
                      type="password"
                      value={formData.confirmPassword}
                      onChange={handleInputChange}
                    />
                  </div>
                </div>

                <Button type="submit">Change Password</Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>


      </Tabs>
    </Layout>
  );
};

export default Profile;
