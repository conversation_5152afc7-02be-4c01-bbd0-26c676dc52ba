import { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import Layout from '@/components/layout/Layout';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Input } from "@/components/ui/input";
import {
  ArrowLeft,
  Calendar,
  DollarSign,
  CreditCard,
  Clock,
  CheckCircle,
  AlertCircle,
  Search,
  Filter
} from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { formatCurrency } from '@/lib/utils';

interface Worker {
  id: string;
  name: string;
  email: string;
  phone: string;
  location: string;
  benefit: number;
  paymentStatus: 'pending' | 'paid';
  totalEarnings: number;
  lastPayment: string;
  avatar?: string;
}

interface PaymentHistory {
  id: string;
  date: string;
  amount: number;
  status: 'pending' | 'paid';
  description: string;
}

const PaymentUser = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [worker, setWorker] = useState<Worker | null>(null);
  const [paymentHistory, setPaymentHistory] = useState<PaymentHistory[]>([]);
  const [loading, setLoading] = useState(true);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [showFinalConfirmDialog, setShowFinalConfirmDialog] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState<PaymentHistory | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Mock data - replace with actual API call
  useEffect(() => {
    const mockWorkers: Worker[] = [
      {
        id: 'WORK-001',
        name: 'Ahmed Benali',
        email: '<EMAIL>',
        phone: '+213 555 123-456',
        location: 'Algiers, Algeria',
        benefit: 1250.00,
        paymentStatus: 'paid',
        totalEarnings: 5200.00,
        lastPayment: '2024-01-15'
      },
      {
        id: 'WORK-002',
        name: 'Fatima Khelil',
        email: '<EMAIL>',
        phone: '+213 555 987-654',
        location: 'Oran, Algeria',
        benefit: 890.50,
        paymentStatus: 'pending',
        totalEarnings: 3400.00,
        lastPayment: '2023-12-20'
      },
      {
        id: 'WORK-003',
        name: 'Mohamed Saidi',
        email: '<EMAIL>',
        phone: '+213 555 456-789',
        location: 'Constantine, Algeria',
        benefit: 1450.75,
        paymentStatus: 'paid',
        totalEarnings: 6800.00,
        lastPayment: '2024-01-10'
      },
      {
        id: 'WORK-004',
        name: 'Amina Boudjema',
        email: '<EMAIL>',
        phone: '+213 555 321-098',
        location: 'Annaba, Algeria',
        benefit: 675.25,
        paymentStatus: 'pending',
        totalEarnings: 2100.00,
        lastPayment: '2023-11-30'
      },
      {
        id: 'WORK-005',
        name: 'Youcef Mansouri',
        email: '<EMAIL>',
        phone: '+213 555 654-321',
        location: 'Setif, Algeria',
        benefit: 1120.00,
        paymentStatus: 'paid',
        totalEarnings: 4500.00,
        lastPayment: '2024-01-12'
      }
    ];

    const mockPaymentHistory: PaymentHistory[] = [
      {
        id: 'PAY-001',
        date: '2024-01-15',
        amount: 1250.00,
        status: 'paid',
        description: 'Monthly commission payment'
      },
      {
        id: 'PAY-002',
        date: '2023-12-15',
        amount: 980.50,
        status: 'paid',
        description: 'Monthly commission payment'
      },
      {
        id: 'PAY-003',
        date: '2023-11-15',
        amount: 1100.00,
        status: 'paid',
        description: 'Monthly commission payment'
      },
      {
        id: 'PAY-004',
        date: '2024-02-15',
        amount: 890.50,
        status: 'pending',
        description: 'Monthly commission payment'
      }
    ];

    setTimeout(() => {
      const foundWorker = mockWorkers.find(w => w.id === id);
      setWorker(foundWorker || null);
      setPaymentHistory(mockPaymentHistory);
      setLoading(false);
    }, 1000);
  }, [id]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handlePaymentStatusChange = (payment: PaymentHistory) => {
    if (payment.status === 'pending') {
      setSelectedPayment(payment);
      setShowConfirmDialog(true);
    }
  };

  const handleFirstConfirmation = () => {
    setShowConfirmDialog(false);
    setShowFinalConfirmDialog(true);
  };

  const confirmPayment = () => {
    if (selectedPayment) {
      // Update payment status
      setPaymentHistory(prev =>
        prev.map(payment =>
          payment.id === selectedPayment.id
            ? { ...payment, status: 'paid' as const }
            : payment
        )
      );

      // Update worker status if this was their current benefit payment
      if (worker && selectedPayment.amount === worker.benefit) {
        setWorker(prev => prev ? { ...prev, paymentStatus: 'paid' as const } : null);
      }
    }
    setShowFinalConfirmDialog(false);
    setShowConfirmDialog(false);
    setSelectedPayment(null);
  };

  const cancelConfirmation = () => {
    setShowConfirmDialog(false);
    setShowFinalConfirmDialog(false);
    setSelectedPayment(null);
  };

  // Filter payment history
  const filteredPaymentHistory = paymentHistory.filter(payment => {
    const matchesSearch = payment.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         payment.amount.toString().includes(searchQuery);
    const matchesStatus = statusFilter === 'all' || payment.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  if (loading) {
    return (
      <Layout>
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading worker details...</p>
        </div>
      </Layout>
    );
  }

  if (!worker) {
    return (
      <Layout>
        <div className="text-center py-8">
          <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Worker Not Found</h2>
          <p className="text-muted-foreground mb-4">The worker you're looking for doesn't exist.</p>
          <Button onClick={() => navigate('/payment')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Payment Management
          </Button>
        </div>
      </Layout>
    );
  }

  const pendingPayments = paymentHistory.filter(p => p.status === 'pending');
  const totalPending = pendingPayments.reduce((sum, payment) => sum + payment.amount, 0);

  return (
    <Layout>
      <div className="mb-4 sm:mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4 mb-4">
          <Button
            variant="outline"
            onClick={() => navigate('/payment')}
            className="flex items-center gap-2 w-fit"
          >
            <ArrowLeft size={16} />
            Back
          </Button>
          <div className="flex-1">
            <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold mb-1">Payment Details</h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              Manage payments for {worker.name}
            </p>
          </div>
        </div>
      </div>

      {/* Worker Info Card */}
      <Card className="mb-4 sm:mb-6">
        <CardHeader className="pb-3 sm:pb-6">
          <CardTitle className="text-lg sm:text-xl">Worker Information</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex items-center gap-3 sm:gap-4">
              <Avatar className="h-12 w-12 sm:h-16 sm:w-16 flex-shrink-0">
                <AvatarImage src={worker.avatar} />
                <AvatarFallback className="text-sm sm:text-lg">
                  {worker.name.split(' ').map(n => n[0]).join('')}
                </AvatarFallback>
              </Avatar>
              <div className="min-w-0 flex-1">
                <h3 className="text-lg sm:text-xl font-semibold truncate">{worker.name}</h3>
                <p className="text-sm sm:text-base text-muted-foreground">ID: {worker.id}</p>
              </div>
            </div>

            <Button
              variant="outline"
              onClick={() => navigate(`/customer/${worker.id}`)}
              className="flex items-center gap-2 w-full sm:w-auto justify-center sm:justify-start"
            >
              View Customer Details
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Payment Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 mb-4 sm:mb-6">
        <Card>
          <CardContent className="p-3 sm:p-4">
            <div className="flex items-center justify-between">
              <div className="min-w-0 flex-1">
                <p className="text-xs sm:text-sm text-muted-foreground">Total Earnings</p>
                <p className="text-lg sm:text-2xl font-bold truncate">{formatCurrency(worker.totalEarnings)}</p>
              </div>
              <CreditCard className="h-6 w-6 sm:h-8 sm:w-8 text-blue-500 flex-shrink-0 ml-2" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-3 sm:p-4">
            <div className="flex items-center justify-between">
              <div className="min-w-0 flex-1">
                <p className="text-xs sm:text-sm text-muted-foreground">Current Benefit</p>
                <p className="text-lg sm:text-2xl font-bold truncate">{formatCurrency(worker.benefit)}</p>
              </div>
              <DollarSign className="h-6 w-6 sm:h-8 sm:w-8 text-green-500 flex-shrink-0 ml-2" />
            </div>
          </CardContent>
        </Card>

        <Card className="sm:col-span-2 lg:col-span-1">
          <CardContent className="p-3 sm:p-4">
            <div className="flex items-center justify-between">
              <div className="min-w-0 flex-1">
                <p className="text-xs sm:text-sm text-muted-foreground">Pending Amount</p>
                <p className="text-lg sm:text-2xl font-bold truncate">{formatCurrency(totalPending)}</p>
              </div>
              <Clock className="h-6 w-6 sm:h-8 sm:w-8 text-yellow-500 flex-shrink-0 ml-2" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Payment History */}
      <Card>
        <CardHeader className="pb-3 sm:pb-6">
          <CardTitle className="text-lg sm:text-xl">Payment History</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 mb-4 sm:mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" size={16} />
              <Input
                placeholder="Search by ID or amount..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9 h-9 sm:h-10 text-sm sm:text-base"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-48 h-9 sm:h-10 text-sm sm:text-base">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="paid">Paid</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Mobile Card View */}
          <div className="block sm:hidden space-y-3">
            {filteredPaymentHistory.map((payment) => (
              <Card key={payment.id} className="p-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Calendar size={14} className="text-muted-foreground" />
                      <span className="text-sm font-medium">
                        {new Date(payment.date).toLocaleDateString()}
                      </span>
                    </div>
                    <Badge className={getStatusColor(payment.status)}>
                      {payment.status}
                    </Badge>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-lg font-bold">{formatCurrency(payment.amount)}</span>
                  </div>

                  <div className="pt-2">
                    {payment.status === 'pending' ? (
                      <Button
                        size="sm"
                        onClick={() => handlePaymentStatusChange(payment)}
                        className="bg-green-600 hover:bg-green-700 w-full"
                      >
                        <CheckCircle size={16} className="mr-2" />
                        Mark as Paid
                      </Button>
                    ) : (
                      <div className="flex justify-center">
                        <Badge variant="outline" className="text-green-600 border-green-600">
                          <CheckCircle size={14} className="mr-1" />
                          Paid
                        </Badge>
                      </div>
                    )}
                  </div>
                </div>
              </Card>
            ))}
          </div>

          {/* Desktop Table View */}
          <div className="hidden sm:block overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="text-sm">Date</TableHead>
                  <TableHead className="text-sm">Amount</TableHead>
                  <TableHead className="text-sm">Status</TableHead>
                  <TableHead className="text-sm">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredPaymentHistory.map((payment) => (
                  <TableRow key={payment.id}>
                    <TableCell className="text-sm">
                      <div className="flex items-center gap-2">
                        <Calendar size={14} className="text-muted-foreground" />
                        {new Date(payment.date).toLocaleDateString()}
                      </div>
                    </TableCell>
                    <TableCell className="font-medium text-sm">{formatCurrency(payment.amount)}</TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(payment.status)}>
                        {payment.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {payment.status === 'pending' ? (
                        <Button
                          size="sm"
                          onClick={() => handlePaymentStatusChange(payment)}
                          className="bg-green-600 hover:bg-green-700 text-xs"
                        >
                          <CheckCircle size={14} className="mr-1" />
                          Mark as Paid
                        </Button>
                      ) : (
                        <Badge variant="outline" className="text-green-600 border-green-600">
                          <CheckCircle size={14} className="mr-1" />
                          Paid
                        </Badge>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* First Confirmation Dialog */}
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent className="bg-white w-[95vw] max-w-md mx-auto">
          <DialogHeader>
            <DialogTitle className="text-lg sm:text-xl">Confirm Payment</DialogTitle>
            <DialogDescription className="text-sm sm:text-base">
              Are you sure you want to mark this payment as paid?
            </DialogDescription>
          </DialogHeader>
          {selectedPayment && (
            <div className="py-3 sm:py-4">
              <div className="bg-gray-50 p-3 sm:p-4 rounded-lg">
                <div className="flex justify-between items-center mb-2">
                  <span className="font-medium text-sm sm:text-base">Amount:</span>
                  <span className="text-base sm:text-lg font-bold">{formatCurrency(selectedPayment.amount)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="font-medium text-sm sm:text-base">Date:</span>
                  <span className="text-sm sm:text-base">{new Date(selectedPayment.date).toLocaleDateString()}</span>
                </div>
              </div>
            </div>
          )}
          <DialogFooter className="gap-2 flex-col sm:flex-row">
            <Button
              variant="outline"
              onClick={cancelConfirmation}
              className="w-full sm:w-auto order-2 sm:order-1"
            >
              Cancel
            </Button>
            <Button
              onClick={handleFirstConfirmation}
              className="bg-yellow-600 hover:bg-yellow-700 w-full sm:w-auto order-1 sm:order-2"
            >
              Continue
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Final Confirmation Dialog */}
      <Dialog open={showFinalConfirmDialog} onOpenChange={setShowFinalConfirmDialog}>
        <DialogContent className="bg-white w-[95vw] max-w-md mx-auto">
          <DialogHeader>
            <DialogTitle className="text-lg sm:text-xl">Final Confirmation</DialogTitle>
            <DialogDescription className="text-sm sm:text-base">
              This action cannot be undone. The payment will be marked as paid permanently.
            </DialogDescription>
          </DialogHeader>
          {selectedPayment && (
            <div className="py-3 sm:py-4">
              <div className="bg-red-50 border border-red-200 p-3 sm:p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-3">
                  <AlertCircle className="h-4 w-4 sm:h-5 sm:w-5 text-red-600 flex-shrink-0" />
                  <span className="font-medium text-red-800 text-sm sm:text-base">Warning: This action is irreversible</span>
                </div>
                <div className="flex justify-between items-center mb-2">
                  <span className="font-medium text-sm sm:text-base">Amount:</span>
                  <span className="text-base sm:text-lg font-bold">{formatCurrency(selectedPayment.amount)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="font-medium text-sm sm:text-base">Date:</span>
                  <span className="text-sm sm:text-base">{new Date(selectedPayment.date).toLocaleDateString()}</span>
                </div>
              </div>
            </div>
          )}
          <DialogFooter className="gap-2 flex-col sm:flex-row">
            <Button
              variant="outline"
              onClick={cancelConfirmation}
              className="w-full sm:w-auto order-2 sm:order-1"
            >
              Cancel
            </Button>
            <Button
              onClick={confirmPayment}
              className="bg-red-600 hover:bg-red-700 w-full sm:w-auto order-1 sm:order-2"
            >
              <CheckCircle size={14} className="mr-2" />
              Confirm Payment
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Layout>
  );
};

export default PaymentUser;
