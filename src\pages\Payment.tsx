import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Layout from '@/components/layout/Layout';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  Search, 
  Filter, 
  Eye, 
  Mail, 
  Phone, 
  MapPin,
  Calendar,
  DollarSign,
  Users,
  CreditCard,
  Clock,
  CheckCircle
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { formatCurrency } from '@/lib/utils';

interface Worker {
  id: string;
  name: string;
  email: string;
  phone: string;
  location: string;
  benefit: number;
  paymentStatus: 'pending' | 'paid';
  totalEarnings: number;
  lastPayment: string;
  avatar?: string;
}

const Payment = () => {
  const navigate = useNavigate();
  const [workers, setWorkers] = useState<Worker[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Mock data - replace with actual API call
  useEffect(() => {
    const mockWorkers: Worker[] = [
      {
        id: 'WORK-001',
        name: 'Ahmed Benali',
        email: '<EMAIL>',
        phone: '+213 555 123-456',
        location: 'Algiers, Algeria',
        benefit: 1250.00,
        paymentStatus: 'paid',
        totalEarnings: 5200.00,
        lastPayment: '2024-01-15'
      },
      {
        id: 'WORK-002',
        name: 'Fatima Khelil',
        email: '<EMAIL>',
        phone: '+213 555 987-654',
        location: 'Oran, Algeria',
        benefit: 890.50,
        paymentStatus: 'pending',
        totalEarnings: 3400.00,
        lastPayment: '2023-12-20'
      },
      {
        id: 'WORK-003',
        name: 'Mohamed Saidi',
        email: '<EMAIL>',
        phone: '+213 555 456-789',
        location: 'Constantine, Algeria',
        benefit: 1450.75,
        paymentStatus: 'paid',
        totalEarnings: 6800.00,
        lastPayment: '2024-01-10'
      },
      {
        id: 'WORK-004',
        name: 'Amina Boudjema',
        email: '<EMAIL>',
        phone: '+213 555 321-098',
        location: 'Annaba, Algeria',
        benefit: 675.25,
        paymentStatus: 'pending',
        totalEarnings: 2100.00,
        lastPayment: '2023-11-30'
      },
      {
        id: 'WORK-005',
        name: 'Youcef Mansouri',
        email: '<EMAIL>',
        phone: '+213 555 654-321',
        location: 'Setif, Algeria',
        benefit: 1120.00,
        paymentStatus: 'paid',
        totalEarnings: 4500.00,
        lastPayment: '2024-01-12'
      }
    ];

    setTimeout(() => {
      setWorkers(mockWorkers);
      setLoading(false);
    }, 1000);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredWorkers = workers.filter(worker => {
    const matchesSearch = worker.name.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === 'all' || worker.paymentStatus === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const totalWorkers = workers.length;
  const paidWorkers = workers.filter(worker => worker.paymentStatus === 'paid').length;
  const pendingWorkers = workers.filter(worker => worker.paymentStatus === 'pending').length;
  const totalBenefits = workers.reduce((sum, worker) => sum + worker.benefit, 0);

  return (
    <Layout>
      <div className="mb-6">
        <h1 className="text-2xl sm:text-3xl font-bold mb-1">Payment Management</h1>
        <p className="text-sm sm:text-base text-muted-foreground">
          Manage worker payments and benefits
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Workers</p>
                <p className="text-2xl font-bold">{totalWorkers}</p>
              </div>
              <Users className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Paid</p>
                <p className="text-2xl font-bold">{paidWorkers}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Pending</p>
                <p className="text-2xl font-bold">{pendingWorkers}</p>
              </div>
              <Clock className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Benefits</p>
                <p className="text-2xl font-bold">{formatCurrency(totalBenefits)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" size={18} />
              <Input
                placeholder="Search workers by name, email, or ID..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="paid">Paid</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Workers Table */}
      <Card>
        <CardHeader>
          <CardTitle>Workers ({filteredWorkers.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="mt-2 text-muted-foreground">Loading workers...</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table className="w-full min-w-full">
                <TableHeader>
                  <TableRow>
                    <TableHead className="min-w-[200px]">Worker</TableHead>
                    <TableHead className="min-w-[160px] whitespace-nowrap">Contact</TableHead>
                    <TableHead className="min-w-[120px] whitespace-nowrap">Benefit</TableHead>
                    <TableHead className="min-w-[100px] whitespace-nowrap">Status</TableHead>
                    <TableHead className="min-w-[80px] whitespace-nowrap">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredWorkers.map((worker) => (
                    <TableRow key={worker.id}>
                      <TableCell className="min-w-[200px]">
                        <div className="flex items-center gap-3">
                          <Avatar>
                            <AvatarImage src={worker.avatar} />
                            <AvatarFallback>
                              {worker.name.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium whitespace-nowrap">{worker.name}</p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="min-w-[160px]">
                        <div className="flex items-center gap-2 text-sm whitespace-nowrap">
                          <Phone size={14} />
                          <span>{worker.phone}</span>
                        </div>
                      </TableCell>
                      <TableCell className="font-medium min-w-[120px] whitespace-nowrap">{formatCurrency(worker.benefit)}</TableCell>
                      <TableCell className="min-w-[100px]">
                        <Badge className={getStatusColor(worker.paymentStatus)}>
                          {worker.paymentStatus}
                        </Badge>
                      </TableCell>
                      <TableCell className="min-w-[80px]">
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => navigate(`/payment/user/${worker.id}`)}
                          >
                            <Eye size={16} />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </Layout>
  );
};

export default Payment;
