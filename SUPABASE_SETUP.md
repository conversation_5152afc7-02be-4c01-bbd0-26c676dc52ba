# Supabase Integration Setup Guide

This guide will help you set up your Supabase database for the admin panel.

## Prerequisites

1. A Supabase account (sign up at https://supabase.com)
2. A Supabase project created

## Database Setup

### Step 1: Create Database Tables

1. Go to your Supabase dashboard
2. Navigate to the SQL Editor
3. Copy and paste the contents of `database-schema.sql` into the SQL editor
4. Click "Run" to execute the script

This will create all necessary tables:
- `categories` - Product categories
- `products` - Products with affiliate pricing
- `customers` - Customer information
- `orders` - Order management
- `order_items` - Individual order items
- `affiliate_workers` - Affiliate worker accounts
- `payments` - Payment tracking
- `delivery_locations` - Algerian wilayas with pricing

### Step 2: Configure Row Level Security (RLS)

For production use, you should enable RLS on your tables. Here are some basic policies:

```sql
-- Enable RLS on all tables
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE affiliate_workers ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE delivery_locations ENABLE ROW LEVEL SECURITY;

-- Example policies (adjust based on your needs)
-- Allow authenticated users to read all data
CREATE POLICY "Allow authenticated read" ON categories FOR SELECT TO authenticated USING (true);
CREATE POLICY "Allow authenticated read" ON products FOR SELECT TO authenticated USING (true);
CREATE POLICY "Allow authenticated read" ON customers FOR SELECT TO authenticated USING (true);
CREATE POLICY "Allow authenticated read" ON orders FOR SELECT TO authenticated USING (true);
CREATE POLICY "Allow authenticated read" ON order_items FOR SELECT TO authenticated USING (true);
CREATE POLICY "Allow authenticated read" ON affiliate_workers FOR SELECT TO authenticated USING (true);
CREATE POLICY "Allow authenticated read" ON payments FOR SELECT TO authenticated USING (true);
CREATE POLICY "Allow authenticated read" ON delivery_locations FOR SELECT TO authenticated USING (true);

-- Allow authenticated users to insert/update/delete (adjust as needed)
CREATE POLICY "Allow authenticated write" ON categories FOR ALL TO authenticated USING (true);
CREATE POLICY "Allow authenticated write" ON products FOR ALL TO authenticated USING (true);
CREATE POLICY "Allow authenticated write" ON customers FOR ALL TO authenticated USING (true);
CREATE POLICY "Allow authenticated write" ON orders FOR ALL TO authenticated USING (true);
CREATE POLICY "Allow authenticated write" ON order_items FOR ALL TO authenticated USING (true);
CREATE POLICY "Allow authenticated write" ON affiliate_workers FOR ALL TO authenticated USING (true);
CREATE POLICY "Allow authenticated write" ON payments FOR ALL TO authenticated USING (true);
CREATE POLICY "Allow authenticated write" ON delivery_locations FOR ALL TO authenticated USING (true);
```

### Step 3: Environment Variables

Your `.env` file has been created with your Supabase credentials:

```
VITE_SUPABASE_URL=https://ycfmaaaxsqyikcyxcpql.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.YyrRPcXOEuIpuzNkABgK0qpwOjc_Gfv2dfz-VXgKRxs
```

## Authentication Setup

### Step 1: Enable Email Authentication

1. Go to Authentication > Settings in your Supabase dashboard
2. Make sure "Enable email confirmations" is configured as needed
3. Configure your site URL (e.g., `http://localhost:5173` for development)

### Step 2: Create Admin User

1. Go to Authentication > Users
2. Click "Add user"
3. Enter email and password for your admin account
4. The user will be created and can log in to the admin panel

## Features Implemented

### ✅ Database Integration
- All tables created with proper relationships
- Supabase client configured
- Environment variables set up

### ✅ Authentication
- Login/Register pages connected to Supabase Auth
- Auth context for managing user state
- Protected routes (can be implemented as needed)

### ✅ API Layer
- Complete API functions for all entities
- Error handling and type safety
- CRUD operations for categories, products, customers, orders, etc.

### ✅ Categories Management
- Real-time CRUD operations
- Image upload support (placeholder URLs for now)
- Enhanced categories with product counts

### 🔄 Next Steps
- Implement image upload to Supabase Storage
- Add real-time subscriptions for live updates
- Implement proper user roles and permissions
- Add data validation and sanitization

## Running the Application

1. Install dependencies:
   ```bash
   npm install
   ```

2. Start the development server:
   ```bash
   npm run dev
   ```

3. Open http://localhost:5173 in your browser

4. Register a new account or log in with existing credentials

## Troubleshooting

### Common Issues

1. **Connection Error**: Make sure your Supabase URL and API key are correct in the `.env` file

2. **Authentication Issues**: Check that email authentication is enabled in your Supabase project

3. **Database Errors**: Ensure all tables are created by running the SQL schema script

4. **CORS Issues**: Make sure your site URL is configured in Supabase Authentication settings

### Getting Help

- Check the Supabase documentation: https://supabase.com/docs
- Review the browser console for error messages
- Check the Supabase dashboard logs for backend errors

## Security Notes

- The current setup uses the anon key which is safe for client-side use
- Implement Row Level Security (RLS) policies for production
- Consider implementing user roles and permissions
- Validate all user inputs on both client and server side
- Use Supabase Storage for secure file uploads in production
