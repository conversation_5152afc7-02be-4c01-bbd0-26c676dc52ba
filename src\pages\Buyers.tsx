
import { useState, useEffect } from 'react';
import Layout from '@/components/layout/Layout';
import { generateMockBuyers, generateRandomData } from '@/lib/utils';
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { formatDate, formatCurrency } from '@/lib/utils';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Search,
  Filter,
  Plus,
  Mail,
  Phone,
  MoreHorizontal,
  Calendar,
  Users,
  DollarSign,
  TrendingUp,
  TrendingDown
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useIsMobile } from '@/hooks/use-mobile';

const Buyers = () => {
  const [allBuyers] = useState(generateMockBuyers(15)); // Generate more buyers for demo
  const [filteredBuyers, setFilteredBuyers] = useState(allBuyers);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [showAddBuyerForm, setShowAddBuyerForm] = useState(false);
  const isMobile = useIsMobile();

  // Get stats data
  const stats = generateRandomData();

  // Filter buyers based on search term and status
  useEffect(() => {
    let result = allBuyers;

    // Filter by search term
    if (searchTerm) {
      result = result.filter(buyer =>
        buyer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        buyer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        buyer.product.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by status
    if (statusFilter !== 'all') {
      result = result.filter(buyer => buyer.status === statusFilter);
    }

    setFilteredBuyers(result);
  }, [searchTerm, statusFilter, allBuyers]);

  const getBadgeVariant = (status: 'pending' | 'confirmed' | 'paid') => {
    switch (status) {
      case 'pending': return 'outline';
      case 'confirmed': return 'secondary';
      case 'paid': return 'success';
      default: return 'outline';
    }
  };

  const getBadgeIcon = (status: 'pending' | 'confirmed' | 'paid') => {
    switch (status) {
      case 'pending': return null;
      case 'confirmed': return <TrendingUp className="h-3 w-3 ml-1" />;
      case 'paid': return <DollarSign className="h-3 w-3 ml-1" />;
      default: return null;
    }
  };

  return (
    <Layout>
      <div className="mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 mb-2">
          <div>
            <h1 className="text-3xl font-bold mb-1">Buyers</h1>
            <p className="text-muted-foreground">
              Manage and track your affiliate sales
            </p>
          </div>
          <Button onClick={() => setShowAddBuyerForm(true)} className="gap-2">
            <Plus className="h-4 w-4" />
            Add New Buyer
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <Card className="p-4">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm text-muted-foreground mb-1">Total Buyers</p>
              <p className="text-2xl font-bold">{stats.totalBuyers}</p>
            </div>
            <div className="bg-primary/10 p-2 rounded-full">
              <Users className="h-5 w-5 text-primary" />
            </div>
          </div>
          <div className="flex items-center mt-2 text-xs">
            <div className={stats.buyersChange.isPositive ? "text-green-600 flex items-center" : "text-red-600 flex items-center"}>
              {stats.buyersChange.isPositive ? <TrendingUp className="h-3 w-3 mr-1" /> : <TrendingDown className="h-3 w-3 mr-1" />}
              {stats.buyersChange.value}%
            </div>
            <span className="text-muted-foreground ml-1">vs last month</span>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm text-muted-foreground mb-1">Total Earnings</p>
              <p className="text-2xl font-bold">{formatCurrency(stats.totalCommission)}</p>
            </div>
            <div className="bg-green-500/10 p-2 rounded-full">
              <DollarSign className="h-5 w-5 text-green-600" />
            </div>
          </div>
          <div className="flex items-center mt-2 text-xs">
            <div className={stats.commissionChange.isPositive ? "text-green-600 flex items-center" : "text-red-600 flex items-center"}>
              {stats.commissionChange.isPositive ? <TrendingUp className="h-3 w-3 mr-1" /> : <TrendingDown className="h-3 w-3 mr-1" />}
              {stats.commissionChange.value}%
            </div>
            <span className="text-muted-foreground ml-1">vs last month</span>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm text-muted-foreground mb-1">Pending Orders</p>
              <p className="text-2xl font-bold">{allBuyers.filter(b => b.status === 'pending').length}</p>
            </div>
            <div className="bg-amber-500/10 p-2 rounded-full">
              <Calendar className="h-5 w-5 text-amber-600" />
            </div>
          </div>
          <div className="flex items-center mt-2 text-xs">
            <span className="text-muted-foreground">Needs your attention</span>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm text-muted-foreground mb-1">Conversion Rate</p>
              <p className="text-2xl font-bold">{(Math.random() * 10 + 5).toFixed(1)}%</p>
            </div>
            <div className="bg-blue-500/10 p-2 rounded-full">
              <TrendingUp className="h-5 w-5 text-blue-600" />
            </div>
          </div>
          <div className="flex items-center mt-2 text-xs">
            <div className="text-green-600 flex items-center">
              <TrendingUp className="h-3 w-3 mr-1" />
              2.1%
            </div>
            <span className="text-muted-foreground ml-1">vs last month</span>
          </div>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search buyers..."
            className="pl-9"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-full sm:w-[180px]">
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4" />
              <span>
                {statusFilter === 'all' ? 'All Statuses' :
                 statusFilter.charAt(0).toUpperCase() + statusFilter.slice(1)}
              </span>
            </div>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
            <SelectItem value="confirmed">Confirmed</SelectItem>
            <SelectItem value="paid">Paid</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Buyers Table */}
      <Card className="overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Product</TableHead>
                {!isMobile && <TableHead>Date</TableHead>}
                <TableHead>Amount</TableHead>
                <TableHead>Your Commission</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredBuyers.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={isMobile ? 6 : 7} className="text-center py-8">
                    <p className="text-muted-foreground">No buyers found</p>
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-4"
                      onClick={() => {
                        setSearchTerm('');
                        setStatusFilter('all');
                      }}
                    >
                      Reset Filters
                    </Button>
                  </TableCell>
                </TableRow>
              ) : (
                filteredBuyers.map((buyer) => (
                  <TableRow key={buyer.id}>
                    <TableCell>
                      <div>
                        <p className="font-medium">{buyer.name}</p>
                        <p className="text-sm text-muted-foreground">{buyer.email}</p>
                      </div>
                    </TableCell>
                    <TableCell>{buyer.product}</TableCell>
                    {!isMobile && <TableCell>{formatDate(buyer.date)}</TableCell>}
                    <TableCell>{formatCurrency(buyer.amount)}</TableCell>
                    <TableCell className="text-green-600 font-medium">{formatCurrency(buyer.commission)}</TableCell>
                    <TableCell>
                      <Badge variant={getBadgeVariant(buyer.status)} className="flex items-center w-fit">
                        {buyer.status.charAt(0).toUpperCase() + buyer.status.slice(1)}
                        {getBadgeIcon(buyer.status)}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem className="gap-2">
                            <Mail className="h-4 w-4" />
                            Email Buyer
                          </DropdownMenuItem>
                          <DropdownMenuItem className="gap-2">
                            <Phone className="h-4 w-4" />
                            Call Buyer
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem className="gap-2 text-red-600">
                            Remove Buyer
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </Card>
    </Layout>
  );
};

export default Buyers;
