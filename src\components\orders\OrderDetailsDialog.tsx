import { useState, useEffect } from 'react';
import * as DialogPrimitive from "@radix-ui/react-dialog";
import {
  Dialog,
  DialogHeader,
  DialogTitle,
  DialogPortal,
  DialogOverlay,
} from "@/components/ui/dialog";
import { cn } from "@/lib/utils";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Package,
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  DollarSign,
  CreditCard,
  Star,
  Save,
  X,
  AlertCircle
} from "lucide-react";
import { formatCurrency } from '@/lib/utils';
import { toast } from 'sonner';
import type { Order } from '@/types';
import { fetchOrderById } from '@/lib/api-extended';

// Using types from @/types instead of local interfaces

interface OrderDetailsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  order: Order | null;
  onStatusUpdate: (orderId: string, newStatus: string) => void;
}

// Custom DialogContent without default close button
const CustomDialogContent = ({ className, children, ...props }: React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>) => (
  <DialogPortal>
    <DialogOverlay />
    <DialogPrimitive.Content
      className={cn(
        "fixed left-[50%] top-[50%] z-50 grid w-[calc(100%-32px)] max-w-lg max-h-[calc(100vh-40px)] translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg overflow-hidden",
        className
      )}
      {...props}
    >
      {children}
    </DialogPrimitive.Content>
  </DialogPortal>
);

const OrderDetailsDialog = ({ open, onOpenChange, order, onStatusUpdate }: OrderDetailsDialogProps) => {
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const [hasStatusChanged, setHasStatusChanged] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [detailedOrder, setDetailedOrder] = useState<Order | null>(null);
  const [loadingDetails, setLoadingDetails] = useState(false);

  // Fetch detailed order data when dialog opens
  useEffect(() => {
    if (open && order && order.id) {
      const loadOrderDetails = async () => {
        try {
          setLoadingDetails(true);
          const orderDetails = await fetchOrderById(order.id);
          setDetailedOrder(orderDetails);
        } catch (error) {
          console.error('Error loading order details:', error);
          toast.error('Failed to load order details');
          setDetailedOrder(order); // Fallback to basic order data
        } finally {
          setLoadingDetails(false);
        }
      };
      loadOrderDetails();
    }
  }, [open, order]);

  if (!order) return null;

  // Use detailed order data if available, otherwise use basic order data
  const currentOrder = detailedOrder || order;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'shipped':
        return 'bg-purple-100 text-purple-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Reset status when order changes
  if (selectedStatus !== order.status && !hasStatusChanged) {
    setSelectedStatus(order.status);
  }

  const handleStatusChange = (newStatus: string) => {
    setSelectedStatus(newStatus);
    setHasStatusChanged(newStatus !== order.status);
  };

  const handleConfirmClick = () => {
    if (!hasStatusChanged) return;
    setShowConfirmDialog(true);
  };

  const handleConfirmStatusUpdate = async () => {
    setIsUpdatingStatus(true);
    setShowConfirmDialog(false);
    try {
      onStatusUpdate(order.id, selectedStatus);
      setHasStatusChanged(false);
      toast.success(`Order status updated to ${selectedStatus}`);
    } catch (error) {
      toast.error('Failed to update order status');
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  const handleCancelStatusChange = () => {
    setSelectedStatus(order.status);
    setHasStatusChanged(false);
  };

  // Calculate subtotal from order items if available
  const subtotal = currentOrder.order_items?.reduce((sum, item) => sum + (item.price * item.quantity), 0) || (currentOrder.total - currentOrder.shipping_cost);
  const commissionAmount = currentOrder.affiliate_worker ? (subtotal * (currentOrder.affiliate_worker.commission_rate || 0)) / 100 : 0;

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <CustomDialogContent className="sm:max-w-[800px] w-[calc(100%-32px)] max-w-[95vw] max-h-[90vh] flex flex-col bg-white p-0 overflow-hidden">
          <DialogHeader className="border-b border-gray-300 pb-4 bg-white flex-shrink-0 px-6 pt-6 relative">
            <DialogTitle className="text-xl flex items-center gap-2 text-black pr-10">
              <Package className="h-6 w-6 text-black" />
              Order Details - {order.id.slice(0, 8)}...
            </DialogTitle>
            {/* Custom close button to ensure proper positioning */}
            <button
              onClick={() => onOpenChange(false)}
              className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none p-1 hover:bg-gray-100"
            >
              <X className="h-4 w-4 text-black" />
              <span className="sr-only">Close</span>
            </button>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto">
            {loadingDetails ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                <span className="ml-2 text-gray-600">Loading order details...</span>
              </div>
            ) : (
              <div className="space-y-6 py-4 px-6">

                {/* Affiliate Worker Information - First */}
                {currentOrder.affiliate_worker && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold flex items-center gap-2 text-black">
                    <Star className="h-5 w-5 text-black" />
                    Affiliate Worker
                  </h3>
                  <div className="border border-gray-300 rounded-lg p-4 bg-white">
                    <div className="space-y-4">
                      <div className="flex items-center gap-3 pb-3 border-b border-gray-200">
                        <Avatar className="h-10 w-10 border border-gray-300">
                          <AvatarFallback className="bg-gray-100 text-black">
                            {currentOrder.affiliate_worker.name?.split(' ').map(n => n[0]).join('') || 'AW'}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium text-black">{currentOrder.affiliate_worker.name}</p>
                          <p className="text-sm text-gray-600">
                            {currentOrder.affiliate_worker.commission_rate || 0}% Commission Rate
                          </p>
                        </div>
                      </div>

                      <div className="space-y-3">
                        <div className="flex items-center gap-3">
                          <Mail size={16} className="text-gray-600" />
                          <span className="text-sm text-black">{currentOrder.affiliate_worker.email}</span>
                        </div>
                        <div className="flex items-center gap-3">
                          <Phone size={16} className="text-gray-600" />
                          <span className="text-sm text-black">{currentOrder.affiliate_worker.phone}</span>
                        </div>
                        <div className="flex items-center gap-3">
                          <DollarSign size={16} className="text-gray-600" />
                          <span className="text-sm text-black">
                            Total Earnings: {formatCurrency(currentOrder.affiliate_worker.total_earnings || 0)}
                          </span>
                        </div>
                        <div className="flex items-center gap-3">
                          <Calendar size={16} className="text-gray-600" />
                          <span className="text-sm text-black">
                            Joined: {currentOrder.affiliate_worker.join_date ? new Date(currentOrder.affiliate_worker.join_date).toLocaleDateString() : 'N/A'}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Order Items - Second */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold flex items-center gap-2 text-black">
                  <Package className="h-5 w-5 text-black" />
                  Order Items ({currentOrder.items_count})
                </h3>
                <div className="border border-gray-300 rounded-lg overflow-hidden bg-white">
                  <div className="space-y-0">
                    {currentOrder.order_items && currentOrder.order_items.length > 0 ? (
                      currentOrder.order_items.map((item, index) => (
                        <div key={item.id} className={`flex items-center gap-4 p-4 ${index !== currentOrder.order_items!.length - 1 ? 'border-b border-gray-200' : ''}`}>
                          <img
                            src={item.product_image || 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=300'}
                            alt={item.product_name || 'Product'}
                            className="w-16 h-16 object-cover rounded-md border border-gray-300"
                          />
                          <div className="flex-1">
                            <h4 className="font-medium text-black">{item.product_name || 'Product'}</h4>
                            <p className="text-sm text-gray-600">
                              Quantity: {item.quantity} × {formatCurrency(item.price)}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="font-medium text-black">{formatCurrency(item.price * item.quantity)}</p>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="p-4 text-center text-gray-500">
                        <Package className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                        <p>Order items details not available</p>
                        <p className="text-sm">Total items: {currentOrder.items_count}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Customer Information - Third */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold flex items-center gap-2 text-black">
                  <User className="h-5 w-5 text-black" />
                  Customer Information
                </h3>
                <div className="border border-gray-300 rounded-lg p-4 bg-white">
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <User size={16} className="text-gray-600" />
                      <span className="font-medium text-black">{currentOrder.customer_name || 'N/A'}</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <Mail size={16} className="text-gray-600" />
                      <span className="text-sm text-black">{currentOrder.customer_email || 'N/A'}</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <Phone size={16} className="text-gray-600" />
                      <span className="text-sm text-black">{currentOrder.customer_phone || 'N/A'}</span>
                    </div>
                    <div className="flex items-start gap-3">
                      <MapPin size={16} className="text-gray-600 mt-0.5" />
                      <span className="text-sm text-black">{currentOrder.customer_location || 'N/A'}</span>
                    </div>
                    {currentOrder.customer_address && (
                      <div className="flex items-start gap-3">
                        <MapPin size={16} className="text-gray-600 mt-0.5" />
                        <span className="text-sm text-black">{currentOrder.customer_address}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Order Summary with Status - Fourth */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold flex items-center gap-2 text-black">
                  <CreditCard className="h-5 w-5 text-black" />
                  Order Summary
                </h3>
                <div className="p-4 border border-gray-300 rounded-lg space-y-4 bg-white">
                  {/* Status Section */}
                  <div className="border-b border-gray-200 pb-4">
                    <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
                      <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium text-black">Status:</span>
                          <Badge className={`${getStatusColor(selectedStatus)} border-0 px-3 py-1.5 text-sm font-medium`}>
                            {selectedStatus.charAt(0).toUpperCase() + selectedStatus.slice(1)}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <Calendar className="h-4 w-4" />
                          <span>Ordered on {new Date(currentOrder.order_date).toLocaleDateString()}</span>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium text-black">Update Status:</span>
                        <Select
                          value={selectedStatus}
                          onValueChange={handleStatusChange}
                          disabled={isUpdatingStatus}
                        >
                          <SelectTrigger className="w-40 border-gray-300">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent className="bg-white border border-gray-300">
                            <SelectItem value="pending">Pending</SelectItem>
                            <SelectItem value="processing">Processing</SelectItem>
                            <SelectItem value="shipped">Shipped</SelectItem>
                            <SelectItem value="delivered">Delivered</SelectItem>
                            <SelectItem value="cancelled">Cancelled</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>

                  {/* Financial Summary */}
                  <div className="space-y-3">
                    <div className="flex justify-between text-black">
                      <span>Subtotal:</span>
                      <span>{formatCurrency(subtotal)}</span>
                    </div>
                    <div className="flex justify-between text-black">
                      <span>Shipping:</span>
                      <span>{formatCurrency(currentOrder.shipping_cost)}</span>
                    </div>
                    <div className="flex justify-between text-black">
                      <span>Affiliate Commission ({currentOrder.affiliate_worker?.commission_rate || 0}%):</span>
                      <span>{formatCurrency(commissionAmount)}</span>
                    </div>
                    <div className="border-t border-gray-200 pt-3">
                      <div className="flex justify-between font-bold text-lg text-black">
                        <span>Total:</span>
                        <span>{formatCurrency(currentOrder.total)}</span>
                      </div>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <CreditCard size={16} />
                      <span>Payment Method: {currentOrder.payment_method}</span>
                    </div>
                  </div>
                </div>
              </div>

              </div>
            )}
          </div>

          {/* Fixed Bottom Action Buttons - Always Visible */}
          <div className="border-t border-gray-300 bg-white p-4 flex items-center justify-end gap-2 flex-shrink-0">
            <Button
              onClick={handleCancelStatusChange}
              disabled={!hasStatusChanged || isUpdatingStatus}
              variant="outline"
              size="sm"
              className={`border-gray-300 text-black ${
                !hasStatusChanged || isUpdatingStatus
                  ? 'opacity-50 cursor-not-allowed hover:bg-white'
                  : 'hover:bg-gray-100'
              }`}
            >
              <X className="h-4 w-4 mr-1" />
              Cancel
            </Button>
            <Button
              onClick={handleConfirmClick}
              disabled={!hasStatusChanged || isUpdatingStatus}
              size="sm"
              className={`border-black ${
                !hasStatusChanged || isUpdatingStatus
                  ? 'bg-gray-400 hover:bg-gray-400 text-gray-200 cursor-not-allowed border-gray-400'
                  : 'bg-black hover:bg-gray-800 text-white'
              }`}
            >
              {isUpdatingStatus ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              ) : (
                <Save className="h-4 w-4 mr-1" />
              )}
              Confirm Status Change
            </Button>
          </div>
        </CustomDialogContent>
      </Dialog>

      {/* Confirmation Dialog */}
      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent className="bg-white border border-gray-300">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-black">Confirm Status Change</AlertDialogTitle>
            <AlertDialogDescription className="text-gray-600">
              Are you sure you want to change the order status from <strong>{order.status}</strong> to <strong>{selectedStatus}</strong>?
              <br />
              <br />
              <span className="text-sm">Order ID: {order.id.slice(0, 8)}...</span>
              <br />
              <span className="text-sm">Customer: {currentOrder.customer_name}</span>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              className="border-gray-300 text-black hover:bg-gray-100"
              onClick={() => setShowConfirmDialog(false)}
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmStatusUpdate}
              className="bg-black hover:bg-gray-800 text-white"
              disabled={isUpdatingStatus}
            >
              {isUpdatingStatus ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              ) : null}
              Confirm Change
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default OrderDetailsDialog;
