import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Layout from '@/components/layout/Layout';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ArrowLeft, Save, Upload, X, Image as ImageIcon, Loader2 } from "lucide-react";
import { toast } from 'sonner';
import { fetchCategories, createProduct } from '@/lib/api';
import { uploadImage } from '@/lib/supabase';
import type { Category, CreateProductForm } from '@/types';

interface ProductForm {
  name: string;
  description: string;
  price: number;
  earningPrice: number;
  category: string;
  stock: number;
  minStock: number;
  mainImage: File | null;
  additionalImages: File[];
  status: 'active' | 'inactive';
}

const NewProduct = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [categoriesLoading, setCategoriesLoading] = useState(true);
  const [categories, setCategories] = useState<Category[]>([]);
  const [product, setProduct] = useState<ProductForm>({
    name: '',
    description: '',
    price: 0,
    earningPrice: 0,
    category: '',
    stock: 0,
    minStock: 5,
    mainImage: null,
    additionalImages: [],
    status: 'active'
  });
  const [mainImagePreview, setMainImagePreview] = useState<string>('');
  const [additionalImagePreviews, setAdditionalImagePreviews] = useState<string[]>([]);

  // Fetch categories when component mounts
  useEffect(() => {
    const loadCategories = async () => {
      try {
        setCategoriesLoading(true);
        const categoriesData = await fetchCategories();
        setCategories(categoriesData);
      } catch (error) {
        console.error('Error fetching categories:', error);
        toast.error('Failed to load categories');
      } finally {
        setCategoriesLoading(false);
      }
    };

    loadCategories();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Validate required fields
      if (!product.name || !product.description || !product.category) {
        toast.error('Please fill in all required fields');
        return;
      }

      if (!product.mainImage) {
        toast.error('Main product image is required');
        return;
      }

      if (product.price <= 0) {
        toast.error('Price must be greater than 0');
        return;
      }

      if (product.earningPrice < 0) {
        toast.error('Earning price cannot be negative');
        return;
      }

      // Find the selected category
      const selectedCategory = categories.find(cat => cat.id === product.category);
      if (!selectedCategory) {
        toast.error('Please select a valid category');
        return;
      }

      // Upload main image
      let mainImageUrl: string;
      try {
        mainImageUrl = await uploadImage(product.mainImage, 'products');
      } catch (error) {
        toast.error('Failed to upload main image');
        return;
      }

      // Upload additional images
      const galleryImageUrls: string[] = [];
      if (product.additionalImages.length > 0) {
        try {
          for (const image of product.additionalImages) {
            const imageUrl = await uploadImage(image, 'products');
            galleryImageUrls.push(imageUrl);
          }
        } catch (error) {
          toast.error('Failed to upload gallery images');
          return;
        }
      }

      // Prepare product data for database
      const productData: CreateProductForm = {
        title: product.name,
        description: product.description,
        price: product.price,
        affiliate_earning_price: product.earningPrice,
        category_id: product.category,
        main_image: mainImageUrl,
        gallery_images: galleryImageUrls,
        in_stock: product.status === 'active',
        stock_count: product.stock,
        free_shipping: false, // Default value
        shipping_cost: 0 // Default value
      };

      // Create product in database
      await createProduct(productData);

      toast.success('Product created successfully!');
      navigate('/products');
    } catch (error) {
      console.error('Error creating product:', error);
      toast.error('Failed to create product. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleMainImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setProduct({ ...product, mainImage: file });

      // Create preview URL
      const reader = new FileReader();
      reader.onload = (e) => {
        setMainImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleAdditionalImagesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      setProduct({
        ...product,
        additionalImages: [...product.additionalImages, ...files]
      });

      // Create preview URLs
      files.forEach(file => {
        const reader = new FileReader();
        reader.onload = (e) => {
          setAdditionalImagePreviews(prev => [...prev, e.target?.result as string]);
        };
        reader.readAsDataURL(file);
      });
    }
  };

  const removeMainImage = () => {
    setProduct({ ...product, mainImage: null });
    setMainImagePreview('');
  };

  const removeAdditionalImage = (index: number) => {
    const newImages = product.additionalImages.filter((_, i) => i !== index);
    const newPreviews = additionalImagePreviews.filter((_, i) => i !== index);

    setProduct({ ...product, additionalImages: newImages });
    setAdditionalImagePreviews(newPreviews);
  };

  return (
    <Layout>
      <div className="mb-6">
        <div className="flex items-center gap-4 mb-4">
          <Button variant="outline" size="sm" onClick={() => navigate('/products')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Products
          </Button>
        </div>
        <h1 className="text-2xl sm:text-3xl font-bold mb-1">Add New Product</h1>
        <p className="text-sm sm:text-base text-muted-foreground">
          Create a new product for your store
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Product Information and Inventory Management - Grid Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Product Information */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Product Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="name">Product Name *</Label>
                  <Input
                    id="name"
                    value={product.name}
                    onChange={(e) => setProduct({...product, name: e.target.value})}
                    placeholder="Enter product name"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="description">Description *</Label>
                  <Textarea
                    id="description"
                    value={product.description}
                    onChange={(e) => setProduct({...product, description: e.target.value})}
                    placeholder="Enter product description"
                    rows={4}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="category">Category *</Label>
                  <Select
                    value={product.category}
                    onValueChange={(value) => setProduct({...product, category: value})}
                    disabled={categoriesLoading}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={categoriesLoading ? "Loading categories..." : "Select category"} />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map(category => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {categoriesLoading && (
                    <div className="flex items-center gap-2 mt-2 text-sm text-muted-foreground">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Loading categories...
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Inventory Management */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle>Inventory Management</CardTitle>
                <p className="text-sm text-muted-foreground">
                  Track stock levels and set alerts
                </p>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="stock" className="text-base font-medium">Current Stock</Label>
                  <Input
                    id="stock"
                    type="number"
                    min="0"
                    value={product.stock}
                    onChange={(e) => setProduct({...product, stock: parseInt(e.target.value) || 0})}
                    placeholder="0"
                    className="h-11"
                  />
                  <p className="text-xs text-muted-foreground">
                    Available quantity in inventory
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="minStock" className="text-base font-medium">Low Stock Alert</Label>
                  <Input
                    id="minStock"
                    type="number"
                    min="0"
                    value={product.minStock}
                    onChange={(e) => setProduct({...product, minStock: parseInt(e.target.value) || 0})}
                    placeholder="5"
                    className="h-11"
                  />
                  <p className="text-xs text-muted-foreground">
                    Get notified when stock falls below this level
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Pricing & Earnings - Full Width */}
        <Card>
          <CardHeader>
            <CardTitle>Pricing & Earnings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <Label htmlFor="price">Selling Price *</Label>
                <Input
                  id="price"
                  type="number"
                  step="0.01"
                  min="0"
                  value={product.price}
                  onChange={(e) => setProduct({...product, price: parseFloat(e.target.value) || 0})}
                  placeholder="0.00"
                  required
                  className="w-full"
                />
              </div>
              <div className="flex-1">
                <Label htmlFor="earningPrice">Affiliate Earning *</Label>
                <Input
                  id="earningPrice"
                  type="number"
                  step="0.01"
                  min="0"
                  value={product.earningPrice}
                  onChange={(e) => setProduct({...product, earningPrice: parseFloat(e.target.value) || 0})}
                  placeholder="0.00"
                  required
                  className="w-full"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Amount affiliates will earn per sale
                </p>
              </div>
            </div>

            {product.earningPrice > 0 && product.price > 0 && (
              <div className="p-3 bg-blue-50 rounded-lg">
                <p className="text-sm text-blue-700">
                  Affiliate Commission: {((product.earningPrice / product.price) * 100).toFixed(1)}% of selling price
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Main Product Image - Full Width */}
        <Card className="border-2 border-dashed border-primary/30 hover:border-primary/50 transition-colors">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ImageIcon className="h-5 w-5 text-primary" />
              Main Product Image *
            </CardTitle>
            <p className="text-sm text-muted-foreground">
              Upload the primary image that will represent your product
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            {!mainImagePreview ? (
              <div
                className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-primary/50 transition-colors cursor-pointer"
                onClick={() => document.getElementById('mainImage')?.click()}
              >
                <div className="flex flex-col items-center space-y-4">
                  <div className="p-4 bg-primary/10 rounded-full">
                    <Upload className="h-8 w-8 text-primary" />
                  </div>
                  <div>
                    <p className="text-lg font-medium text-primary">
                      Click to upload main image
                    </p>
                    <p className="text-sm text-muted-foreground mt-1">
                      PNG, JPG, GIF up to 10MB
                    </p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="relative w-full max-w-md mx-auto">
                  <div className="relative group">
                    <img
                      src={mainImagePreview}
                      alt="Main product image"
                      className="w-full h-64 object-cover rounded-lg border-2 border-primary/30 shadow-md"
                    />
                    <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                      <Button
                        type="button"
                        variant="destructive"
                        size="sm"
                        onClick={removeMainImage}
                        className="bg-red-500 hover:bg-red-600"
                      >
                        <X className="h-4 w-4 mr-2" />
                        Remove
                      </Button>
                    </div>
                  </div>
                  <div className="absolute top-2 left-2 bg-primary text-primary-foreground px-2 py-1 rounded text-xs font-medium">
                    Main Image
                  </div>
                </div>
                <div className="text-center">
                  <button
                    type="button"
                    onClick={() => document.getElementById('mainImageReplace')?.click()}
                    className="text-sm text-primary hover:text-primary/80 underline"
                  >
                    Change main image
                  </button>
                </div>
              </div>
            )}

            <Input
              id="mainImage"
              type="file"
              accept="image/*"
              onChange={handleMainImageChange}
              className="hidden"
              required={!product.mainImage}
            />

            <Input
              id="mainImageReplace"
              type="file"
              accept="image/*"
              onChange={handleMainImageChange}
              className="hidden"
            />
          </CardContent>
        </Card>

        {/* Additional Product Images - Full Width */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              Additional Images (Optional)
            </CardTitle>
            <p className="text-sm text-muted-foreground">
              Add more images to showcase your product from different angles
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            <div
              className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary/50 transition-colors cursor-pointer"
              onClick={() => document.getElementById('additionalImages')?.click()}
            >
              <div className="flex flex-col items-center space-y-3">
                <div className="p-3 bg-gray-100 rounded-full">
                  <Upload className="h-6 w-6 text-gray-600" />
                </div>
                <div>
                  <p className="text-base font-medium text-gray-700">
                    Upload gallery images
                  </p>
                  <p className="text-sm text-muted-foreground mt-1">
                    Select multiple images for the product gallery
                  </p>
                </div>
              </div>
            </div>

            <Input
              id="additionalImages"
              type="file"
              accept="image/*"
              multiple
              onChange={handleAdditionalImagesChange}
              className="hidden"
            />

            {additionalImagePreviews.length > 0 && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label className="text-base font-medium">Gallery Images</Label>
                  <span className="text-sm text-muted-foreground bg-gray-100 px-2 py-1 rounded">
                    {additionalImagePreviews.length} image{additionalImagePreviews.length !== 1 ? 's' : ''}
                  </span>
                </div>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {additionalImagePreviews.map((preview, index) => (
                    <div key={index} className="relative group">
                      <img
                        src={preview}
                        alt={`Gallery image ${index + 1}`}
                        className="w-full h-28 object-cover rounded-lg border-2 border-gray-200 shadow-sm"
                      />
                      <Button
                        type="button"
                        variant="destructive"
                        size="sm"
                        className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity h-7 w-7 p-0 bg-red-500 hover:bg-red-600"
                        onClick={() => removeAdditionalImage(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                      <div className="absolute bottom-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-xs font-medium">
                        {index + 1}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Action Buttons - Fixed at bottom */}
        <div className="mt-8 pt-6 border-t bg-white/95 backdrop-blur-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex flex-col sm:flex-row gap-3 justify-end">
              <Button
                type="button"
                variant="outline"
                size="lg"
                className="sm:w-auto w-full order-2 sm:order-1"
                onClick={() => navigate('/products')}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                size="lg"
                className="sm:w-auto w-full order-1 sm:order-2"
                disabled={loading || categoriesLoading}
              >
                {loading ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Creating Product...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Create Product
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </form>
    </Layout>
  );
};

export default NewProduct;