import { useState, useEffect } from 'react';
import Layout from '@/components/layout/Layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Search,
  Save,
  Truck,
  Home,
  Building2,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { algeriaWilayas, Wilaya } from '@/data/algeria-locations';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { fetchDeliveryLocations, batchUpdateDeliveryLocations } from '@/lib/api-extended';
import type { DeliveryLocation } from '@/types';

interface DeliveryPrices {
  [wilayaId: number]: {
    id?: string;
    homeDeliveryPrice: number;
    deskDeliveryPrice: number;
    deliveryEnabled: boolean;
    hasChanges: boolean;
    originalData?: {
      homeDeliveryPrice: number;
      deskDeliveryPrice: number;
      deliveryEnabled: boolean;
    };
  };
}

const Delivery = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [deliveryPrices, setDeliveryPrices] = useState<DeliveryPrices>({});
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [hasAnyChanges, setHasAnyChanges] = useState(false);
  const [dbLocations, setDbLocations] = useState<DeliveryLocation[]>([]);

  // Initialize delivery prices from the database
  useEffect(() => {
    const loadDeliveryLocations = async () => {
      try {
        setInitialLoading(true);
        const locations = await fetchDeliveryLocations();
        setDbLocations(locations);

        const initialPrices: DeliveryPrices = {};

        // Create a map of database locations by wilaya code for quick lookup
        const dbLocationMap = new Map(locations.map(loc => [loc.wilaya_code, loc]));

        algeriaWilayas.forEach(wilaya => {
          const dbLocation = dbLocationMap.get(wilaya.id);

          if (dbLocation) {
            // Use database values if available
            initialPrices[wilaya.id] = {
              id: dbLocation.id,
              homeDeliveryPrice: dbLocation.domicile_price,
              deskDeliveryPrice: dbLocation.desk_price,
              deliveryEnabled: dbLocation.enabled,
              hasChanges: false,
              originalData: {
                homeDeliveryPrice: dbLocation.domicile_price,
                deskDeliveryPrice: dbLocation.desk_price,
                deliveryEnabled: dbLocation.enabled
              }
            };
          } else {
            // Fallback to static data if not in database
            initialPrices[wilaya.id] = {
              homeDeliveryPrice: wilaya.homeDeliveryPrice,
              deskDeliveryPrice: wilaya.deskDeliveryPrice,
              deliveryEnabled: true,
              hasChanges: false,
              originalData: {
                homeDeliveryPrice: wilaya.homeDeliveryPrice,
                deskDeliveryPrice: wilaya.deskDeliveryPrice,
                deliveryEnabled: true
              }
            };
          }
        });

        setDeliveryPrices(initialPrices);
      } catch (error) {
        console.error('Failed to load delivery locations:', error);
        toast.error('Failed to load delivery locations. Please refresh the page.');

        // Fallback to static data
        const fallbackPrices: DeliveryPrices = {};
        algeriaWilayas.forEach(wilaya => {
          fallbackPrices[wilaya.id] = {
            homeDeliveryPrice: wilaya.homeDeliveryPrice,
            deskDeliveryPrice: wilaya.deskDeliveryPrice,
            deliveryEnabled: true,
            hasChanges: false,
            originalData: {
              homeDeliveryPrice: wilaya.homeDeliveryPrice,
              deskDeliveryPrice: wilaya.deskDeliveryPrice,
              deliveryEnabled: true
            }
          };
        });
        setDeliveryPrices(fallbackPrices);
      } finally {
        setInitialLoading(false);
      }
    };

    loadDeliveryLocations();
  }, []);

  // Filter wilayas based on search term (name or code)
  const filteredWilayas = algeriaWilayas.filter(wilaya => {
    const searchLower = searchTerm.toLowerCase();
    const wilayaName = wilaya.name.toLowerCase();
    const wilayaCode = wilaya.id.toString().padStart(2, '0');

    return wilayaName.includes(searchLower) ||
           wilayaCode.includes(searchTerm) ||
           wilaya.id.toString().includes(searchTerm);
  });

  // Update delivery price
  const updatePrice = (wilayaId: number, field: 'homeDeliveryPrice' | 'deskDeliveryPrice', value: string) => {
    const numericValue = parseFloat(value) || 0;

    setDeliveryPrices(prev => {
      const currentData = prev[wilayaId];
      if (!currentData || !currentData.originalData) return prev;

      const originalData = currentData.originalData;

      // Check if any field has changed from original values
      const homeChanged = field === 'homeDeliveryPrice' ?
        numericValue !== originalData.homeDeliveryPrice :
        currentData.homeDeliveryPrice !== originalData.homeDeliveryPrice;

      const deskChanged = field === 'deskDeliveryPrice' ?
        numericValue !== originalData.deskDeliveryPrice :
        currentData.deskDeliveryPrice !== originalData.deskDeliveryPrice;

      const enabledChanged = currentData.deliveryEnabled !== originalData.deliveryEnabled;

      const hasChanges = homeChanged || deskChanged || enabledChanged;

      const newPrices = {
        ...prev,
        [wilayaId]: {
          ...prev[wilayaId],
          [field]: numericValue,
          hasChanges
        }
      };

      // Check if any wilaya has changes
      const anyChanges = Object.values(newPrices).some(price => price.hasChanges);
      setHasAnyChanges(anyChanges);

      return newPrices;
    });
  };

  // Update delivery status
  const updateDeliveryStatus = (wilayaId: number, enabled: boolean) => {
    setDeliveryPrices(prev => {
      const currentData = prev[wilayaId];
      if (!currentData || !currentData.originalData) return prev;

      const originalData = currentData.originalData;

      // Check if any field has changed from original values
      const homeChanged = currentData.homeDeliveryPrice !== originalData.homeDeliveryPrice;
      const deskChanged = currentData.deskDeliveryPrice !== originalData.deskDeliveryPrice;
      const enabledChanged = enabled !== originalData.deliveryEnabled;

      const hasChanges = homeChanged || deskChanged || enabledChanged;

      const newPrices = {
        ...prev,
        [wilayaId]: {
          ...prev[wilayaId],
          deliveryEnabled: enabled,
          hasChanges
        }
      };

      // Check if any wilaya has changes
      const anyChanges = Object.values(newPrices).some(price => price.hasChanges);
      setHasAnyChanges(anyChanges);

      return newPrices;
    });
  };

  // Save all changes
  const handleSaveAll = async () => {
    setLoading(true);

    try {
      // Prepare updates for changed wilayas
      const updates = Object.entries(deliveryPrices)
        .filter(([_, data]) => data.hasChanges)
        .map(([wilayaId, data]) => ({
          wilayaCode: parseInt(wilayaId),
          data: {
            desk_price: data.deskDeliveryPrice,
            domicile_price: data.homeDeliveryPrice,
            enabled: data.deliveryEnabled
          }
        }));

      if (updates.length === 0) {
        toast.info('No changes to save');
        setLoading(false);
        return;
      }

      // Update database
      await batchUpdateDeliveryLocations(updates);

      // Update local state to reflect saved changes
      setDeliveryPrices(prev => {
        const updated = { ...prev };
        Object.keys(updated).forEach(key => {
          const wilayaId = parseInt(key);
          const data = updated[wilayaId];
          if (data.hasChanges) {
            // Update original data to current values
            data.originalData = {
              homeDeliveryPrice: data.homeDeliveryPrice,
              deskDeliveryPrice: data.deskDeliveryPrice,
              deliveryEnabled: data.deliveryEnabled
            };
            data.hasChanges = false;
          }
        });
        return updated;
      });

      setHasAnyChanges(false);
      toast.success(`Successfully updated ${updates.length} delivery location${updates.length !== 1 ? 's' : ''}!`);
    } catch (error) {
      console.error('Failed to update delivery prices:', error);
      toast.error('Failed to update delivery prices. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Format currency for display
  const formatCurrency = (amount: number) => {
    return `${amount.toFixed(0)} DA`;
  };

  // Count wilayas with changes
  const changedWilayasCount = Object.values(deliveryPrices).filter(p => p.hasChanges).length;

  // Count enabled wilayas
  const enabledWilayasCount = Object.values(deliveryPrices).filter(p => p.deliveryEnabled).length;

  // Show loading state while fetching initial data
  if (initialLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="flex flex-col items-center gap-4">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <p className="text-muted-foreground">Loading delivery locations...</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="mb-6 overflow-hidden">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold mb-1 flex items-center gap-2">
              <Truck className="h-8 w-8 text-primary" />
              Delivery Management
            </h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              Configure delivery prices for all 58 Algerian wilayas
            </p>
          </div>
        </div>
      </div>

      {/* Stats Section */}
      <div className="space-y-4 mb-6">
        {/* Row 1: Stats Cards (Horizontal) */}
        <div className="grid grid-cols-2 gap-3 sm:gap-4">
          <Card>
            <CardContent className="p-3 sm:p-4">
              <div className="flex items-center gap-2">
                <Building2 className="h-4 w-4 sm:h-5 sm:w-5 text-blue-500 flex-shrink-0" />
                <div className="min-w-0">
                  <p className="text-xs sm:text-sm text-muted-foreground truncate">Total Wilayas</p>
                  <p className="text-lg sm:text-xl font-bold">{algeriaWilayas.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-3 sm:p-4">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 sm:h-5 sm:w-5 text-green-500 flex-shrink-0" />
                <div className="min-w-0">
                  <p className="text-xs sm:text-sm text-muted-foreground truncate">Enabled Wilayas</p>
                  <p className="text-lg sm:text-xl font-bold">
                    {enabledWilayasCount}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Row 2: Search Area (Full Width) */}
        <Card>
          <CardContent className="p-3 sm:p-4">
            <div className="space-y-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search by wilaya name or code (e.g., 'Algiers' or '16')..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 text-sm"
                />
              </div>
              {searchTerm && (
                <p className="text-xs text-muted-foreground">
                  {filteredWilayas.length} wilaya{filteredWilayas.length !== 1 ? 's' : ''} found
                  {filteredWilayas.length !== algeriaWilayas.length && (
                    <span className="ml-1">
                      (of {algeriaWilayas.length} total)
                    </span>
                  )}
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Delivery Prices Table */}
      <Card className="overflow-hidden mb-4">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Truck className="h-5 w-5" />
            Delivery Prices
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0 overflow-hidden">
          <div className="overflow-x-auto scrollbar-hide">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="min-w-[160px] sm:w-[200px]">Wilaya</TableHead>
                  <TableHead className="min-w-[120px] sm:w-[150px]">
                    <div className="flex items-center gap-1">
                      <Home className="h-3 w-3 sm:h-4 sm:w-4" />
                      <span className="text-xs sm:text-sm">Domicile</span>
                    </div>
                  </TableHead>
                  <TableHead className="min-w-[120px] sm:w-[150px]">
                    <div className="flex items-center gap-1">
                      <Building2 className="h-3 w-3 sm:h-4 sm:w-4" />
                      <span className="text-xs sm:text-sm">Desk</span>
                    </div>
                  </TableHead>
                  <TableHead className="min-w-[100px] sm:w-[120px]">
                    <span className="text-xs sm:text-sm">Available</span>
                  </TableHead>
                  <TableHead className="min-w-[80px] sm:w-[100px]">
                    <span className="text-xs sm:text-sm">Status</span>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredWilayas.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-8">
                      <div className="flex flex-col items-center gap-2 text-muted-foreground">
                        <Search className="h-8 w-8" />
                        <p className="text-sm">No wilayas found matching "{searchTerm}"</p>
                        <p className="text-xs">Try searching by name or code (e.g., 'Algiers' or '16')</p>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredWilayas.map((wilaya) => {
                    const prices = deliveryPrices[wilaya.id];
                    if (!prices) return null;

                    return (
                    <TableRow key={wilaya.id} className={cn(
                      prices.hasChanges && "bg-orange-50 dark:bg-orange-950/20 border-l-4 border-l-orange-400"
                    )}>
                      <TableCell className="font-medium">
                        <div className={cn(!prices.deliveryEnabled && "opacity-60")}>
                          <p className={cn(
                            "font-semibold text-sm sm:text-base truncate",
                            !prices.deliveryEnabled && "text-muted-foreground"
                          )}>
                            {wilaya.name}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Code: {wilaya.id.toString().padStart(2, '0')}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className={cn("space-y-1", !prices.deliveryEnabled && "opacity-60")}>
                          <Input
                            type="number"
                            value={prices.homeDeliveryPrice}
                            onChange={(e) => updatePrice(wilaya.id, 'homeDeliveryPrice', e.target.value)}
                            disabled={!prices.deliveryEnabled}
                            className={cn(
                              "w-full text-sm h-8 sm:h-10",
                              prices.hasChanges && "border-orange-300 focus:border-orange-400 focus:ring-orange-200",
                              !prices.deliveryEnabled && "bg-muted cursor-not-allowed"
                            )}
                            min="0"
                            step="50"
                          />
                          <p className="text-xs text-muted-foreground hidden sm:block">
                            {formatCurrency(prices.homeDeliveryPrice)}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className={cn("space-y-1", !prices.deliveryEnabled && "opacity-60")}>
                          <Input
                            type="number"
                            value={prices.deskDeliveryPrice}
                            onChange={(e) => updatePrice(wilaya.id, 'deskDeliveryPrice', e.target.value)}
                            disabled={!prices.deliveryEnabled}
                            className={cn(
                              "w-full text-sm h-8 sm:h-10",
                              prices.hasChanges && "border-orange-300 focus:border-orange-400 focus:ring-orange-200",
                              !prices.deliveryEnabled && "bg-muted cursor-not-allowed"
                            )}
                            min="0"
                            step="50"
                          />
                          <p className="text-xs text-muted-foreground hidden sm:block">
                            {formatCurrency(prices.deskDeliveryPrice)}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center justify-center">
                          <Switch
                            checked={prices.deliveryEnabled}
                            onCheckedChange={(checked) => updateDeliveryStatus(wilaya.id, checked)}
                            className={cn(
                              "data-[state=checked]:bg-primary data-[state=unchecked]:bg-input scale-75 sm:scale-100",
                              "opacity-100" // Ensure toggle is always fully visible
                            )}
                          />
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="opacity-100"> {/* Ensure status badges remain fully visible */}
                          {!prices.deliveryEnabled ? (
                            <Badge variant="outline" className="text-gray-500 border-gray-300 text-xs">
                              <AlertCircle className="h-2 w-2 sm:h-3 sm:w-3 mr-1" />
                              <span className="hidden sm:inline">Disabled</span>
                              <span className="sm:hidden">Off</span>
                            </Badge>
                          ) : prices.hasChanges ? (
                            <Badge variant="outline" className="text-orange-600 border-orange-200 text-xs">
                              <AlertCircle className="h-2 w-2 sm:h-3 sm:w-3 mr-1" />
                              <span className="hidden sm:inline">Modified</span>
                              <span className="sm:hidden">Mod</span>
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="text-green-600 border-green-200 text-xs">
                              <CheckCircle className="h-2 w-2 sm:h-3 sm:w-3 mr-1" />
                              <span className="hidden sm:inline">Saved</span>
                              <span className="sm:hidden">OK</span>
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                    );
                  })
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Fixed Save Button */}
      <div className={cn(
        "fixed bottom-0 left-0 right-0 z-50 transition-all duration-300 ease-in-out",
        hasAnyChanges ? "translate-y-0" : "translate-y-full"
      )}>
        <div className="bg-background/95 backdrop-blur-sm border-t border-border shadow-lg">
          <div className="container mx-auto px-3 sm:px-4 py-3 sm:py-4">
            <div className="flex flex-col sm:flex-row items-center justify-between gap-3">
              <div className="flex items-center gap-2 text-xs sm:text-sm text-muted-foreground">
                <AlertCircle className="h-3 w-3 sm:h-4 sm:w-4 text-orange-500 flex-shrink-0" />
                <span className="text-center sm:text-left">
                  {changedWilayasCount} wilaya{changedWilayasCount !== 1 ? 's' : ''} with unsaved changes
                </span>
              </div>

              <div className="flex items-center gap-2 sm:gap-3 w-full sm:w-auto">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    // Reset all changes to original database values
                    setDeliveryPrices(prev => {
                      const reset = { ...prev };
                      Object.keys(reset).forEach(key => {
                        const wilayaId = parseInt(key);
                        const data = reset[wilayaId];
                        if (data.originalData) {
                          reset[wilayaId] = {
                            ...data,
                            homeDeliveryPrice: data.originalData.homeDeliveryPrice,
                            deskDeliveryPrice: data.originalData.deskDeliveryPrice,
                            deliveryEnabled: data.originalData.deliveryEnabled,
                            hasChanges: false
                          };
                        }
                      });
                      return reset;
                    });
                    setHasAnyChanges(false);
                    toast.info('All changes have been discarded');
                  }}
                  disabled={loading}
                  className="flex-1 sm:flex-none text-xs sm:text-sm"
                >
                  <span className="hidden sm:inline">Discard Changes</span>
                  <span className="sm:hidden">Discard</span>
                </Button>

                <Button
                  size="sm"
                  onClick={handleSaveAll}
                  disabled={!hasAnyChanges || loading}
                  className={cn(
                    "flex-1 sm:flex-none min-w-[100px] sm:min-w-[140px] text-xs sm:text-sm",
                    hasAnyChanges && !loading && "bg-primary hover:bg-primary/90 text-primary-foreground"
                  )}
                >
                  {loading ? (
                    <>
                      <Loader2 className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 animate-spin" />
                      <span className="hidden sm:inline">Saving...</span>
                      <span className="sm:hidden">Save</span>
                    </>
                  ) : (
                    <>
                      <Save className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                      <span className="hidden sm:inline">Save All Changes</span>
                      <span className="sm:hidden">Save All</span>
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom padding to prevent content from being hidden behind fixed button */}
      <div className={cn(
        "transition-all duration-300",
        hasAnyChanges ? "h-16 sm:h-20" : "h-0"
      )} />
    </Layout>
  );
};

export default Delivery;
