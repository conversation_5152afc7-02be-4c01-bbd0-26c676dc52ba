// Extended API functions for Supabase backend
import { supabase } from './supabase';
import type { 
  AffiliateWorker, 
  Order, 
  OrderItem, 
  DeliveryLocation, 
  Payment,
  DashboardStats,
  CreateOrderForm
} from '../types';

// Utility function to handle Supabase errors
function handleSupabaseError(error: any, operation: string) {
  console.error(`Error in ${operation}:`, error);
  throw new Error(error.message || `Failed to ${operation}`);
}

// ===== AFFILIATE WORKERS API =====

// Function to fetch all affiliate workers
export async function fetchAffiliateWorkers(): Promise<AffiliateWorker[]> {
  try {
    const { data, error } = await supabase
      .from('affiliate_workers')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  } catch (error) {
    handleSupabaseError(error, 'fetch affiliate workers');
    return []; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// Function to fetch a single affiliate worker by ID
export async function fetchAffiliateWorkerById(id: string): Promise<AffiliateWorker> {
  try {
    const { data, error } = await supabase
      .from('affiliate_workers')
      .select('*')
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    handleSupabaseError(error, `fetch affiliate worker with ID ${id}`);
    throw error; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// ===== DEBUG FUNCTIONS =====

// Function to test database connectivity and table structure
export async function testDatabaseStructure() {
  try {
    console.log('Testing database structure...');

    // Test orders table
    const { data: ordersData, error: ordersError } = await supabase
      .from('orders')
      .select('*')
      .limit(1);

    if (ordersError) {
      console.error('Orders table error:', ordersError);
    } else {
      console.log('Orders table sample:', ordersData);
    }

    // Test customers table
    const { data: customersData, error: customersError } = await supabase
      .from('customers')
      .select('*')
      .limit(1);

    if (customersError) {
      console.error('Customers table error:', customersError);
    } else {
      console.log('Customers table sample:', customersData);
    }

  } catch (error) {
    console.error('Database test error:', error);
  }
}

// ===== ORDERS API =====

// Function to fetch all orders with related data
export async function fetchOrders(): Promise<Order[]> {
  try {
    // First try with just the name column to see if customers table exists
    const { data, error } = await supabase
      .from('orders')
      .select(`
        *,
        customers(name)
      `)
      .order('created_at', { ascending: false });

    if (error) throw error;

    return data.map((order: any) => ({
      ...order,
      customer_name: order.customers?.name || 'Unknown Customer',
      customer_email: 'N/A', // Will be populated when we figure out the correct column
      customer_phone: 'N/A',
      customer_location: 'N/A',
      customer_address: 'N/A',
      affiliate_worker: null
    }));
  } catch (error) {
    handleSupabaseError(error, 'fetch orders');
    return []; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// Function to fetch a single order by ID with all related data
export async function fetchOrderById(id: string): Promise<Order> {
  try {
    const { data, error } = await supabase
      .from('orders')
      .select(`
        *,
        customers(name, email, phone, location, address),
        order_items(
          *,
          products(title, main_image)
        )
      `)
      .eq('id', id)
      .single();

    if (error) throw error;

    return {
      ...data,
      customer_name: data.customers?.name || 'Unknown Customer',
      customer_email: data.customers?.email || '<EMAIL>',
      customer_phone: data.customers?.phone || '',
      customer_location: data.customers?.location || '',
      customer_address: data.customers?.address || '',
      affiliate_worker: null, // Will be populated separately if needed
      order_items: data.order_items?.map((item: any) => ({
        ...item,
        product_name: item.products?.title || 'Unknown Product',
        product_image: item.products?.main_image || 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=300'
      })) || []
    };
  } catch (error) {
    handleSupabaseError(error, `fetch order with ID ${id}`);
    throw error; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// Function to create a new order
export async function createOrder(orderData: CreateOrderForm): Promise<Order> {
  try {
    // Calculate total from items
    const total = orderData.items.reduce((sum, item) => sum + (item.price * item.quantity), 0) + orderData.shipping_cost;

    // Create the order
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .insert({
        customer_id: orderData.customer_id,
        affiliate_worker_id: orderData.affiliate_worker_id,
        order_date: new Date().toISOString(),
        status: 'pending',
        total,
        items_count: orderData.items.reduce((sum, item) => sum + item.quantity, 0),
        payment_method: orderData.payment_method,
        shipping_cost: orderData.shipping_cost,
        notes: orderData.notes
      })
      .select()
      .single();

    if (orderError) throw orderError;

    // Create order items
    const orderItems = orderData.items.map(item => ({
      order_id: order.id,
      product_id: item.product_id,
      quantity: item.quantity,
      price: item.price,
      affiliate_earnings: 0 // Calculate based on affiliate commission
    }));

    const { error: itemsError } = await supabase
      .from('order_items')
      .insert(orderItems);

    if (itemsError) throw itemsError;

    return await fetchOrderById(order.id);
  } catch (error) {
    handleSupabaseError(error, 'create order');
    throw error; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// Function to update order status
export async function updateOrderStatus(id: string, status: Order['status']): Promise<Order> {
  try {
    const { error } = await supabase
      .from('orders')
      .update({ 
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', id);

    if (error) throw error;
    return await fetchOrderById(id);
  } catch (error) {
    handleSupabaseError(error, 'update order status');
    throw error; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// ===== DELIVERY LOCATIONS API =====

// Function to fetch all delivery locations
export async function fetchDeliveryLocations(): Promise<DeliveryLocation[]> {
  try {
    const { data, error } = await supabase
      .from('delivery_locations')
      .select('*')
      .order('wilaya_code');

    if (error) throw error;
    return data;
  } catch (error) {
    handleSupabaseError(error, 'fetch delivery locations');
    return []; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// Function to update delivery location pricing
export async function updateDeliveryLocation(id: string, locationData: Partial<DeliveryLocation>): Promise<DeliveryLocation> {
  try {
    const { data, error } = await supabase
      .from('delivery_locations')
      .update({
        ...locationData,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    handleSupabaseError(error, 'update delivery location');
    throw error; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// Function to update delivery location by wilaya code
export async function updateDeliveryLocationByWilayaCode(wilayaCode: number, locationData: Partial<DeliveryLocation>): Promise<DeliveryLocation> {
  try {
    const { data, error } = await supabase
      .from('delivery_locations')
      .update({
        ...locationData,
        updated_at: new Date().toISOString()
      })
      .eq('wilaya_code', wilayaCode)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    handleSupabaseError(error, 'update delivery location by wilaya code');
    throw error; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// Function to batch update multiple delivery locations
export async function batchUpdateDeliveryLocations(updates: Array<{ wilayaCode: number; data: Partial<DeliveryLocation> }>): Promise<DeliveryLocation[]> {
  try {
    const results: DeliveryLocation[] = [];

    // Process updates in batches to avoid overwhelming the database
    for (const update of updates) {
      const result = await updateDeliveryLocationByWilayaCode(update.wilayaCode, update.data);
      results.push(result);
    }

    return results;
  } catch (error) {
    handleSupabaseError(error, 'batch update delivery locations');
    throw error; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// ===== PAYMENTS API =====

// Function to fetch all payments
export async function fetchPayments(): Promise<Payment[]> {
  try {
    const { data, error } = await supabase
      .from('payments')
      .select(`
        *,
        affiliate_workers!inner(name, email, phone, location)
      `)
      .order('created_at', { ascending: false });

    if (error) throw error;

    return data.map((payment: any) => ({
      ...payment,
      affiliate_worker: payment.affiliate_workers
    }));
  } catch (error) {
    handleSupabaseError(error, 'fetch payments');
    return []; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// Function to create a new payment
export async function createPayment(affiliateWorkerId: string, amount: number): Promise<Payment> {
  try {
    const { data, error } = await supabase
      .from('payments')
      .insert({
        affiliate_worker_id: affiliateWorkerId,
        amount,
        payment_date: new Date().toISOString(),
        status: 'pending'
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    handleSupabaseError(error, 'create payment');
    throw error; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// Function to update payment status
export async function updatePaymentStatus(id: string, status: Payment['status']): Promise<Payment> {
  try {
    const { data, error } = await supabase
      .from('payments')
      .update({ 
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    handleSupabaseError(error, 'update payment status');
    throw error; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// ===== DASHBOARD STATS API =====

// Function to get dashboard statistics
export async function fetchDashboardStats(): Promise<DashboardStats> {
  try {
    // Get total revenue from orders
    const { data: revenueData } = await supabase
      .from('orders')
      .select('total')
      .eq('status', 'delivered');

    const totalRevenue = revenueData?.reduce((sum, order) => sum + order.total, 0) || 0;

    // Get total orders count
    const { count: totalOrders } = await supabase
      .from('orders')
      .select('*', { count: 'exact', head: true });

    // Get total customers count
    const { count: totalCustomers } = await supabase
      .from('customers')
      .select('*', { count: 'exact', head: true });

    // Calculate conversion rate (delivered orders / total orders)
    const { count: deliveredOrders } = await supabase
      .from('orders')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'delivered');

    const conversionRate = totalOrders ? (deliveredOrders || 0) / totalOrders * 100 : 0;

    return {
      totalRevenue,
      totalOrders: totalOrders || 0,
      totalCustomers: totalCustomers || 0,
      conversionRate,
      revenueGrowth: 0, // TODO: Calculate based on previous period
      ordersGrowth: 0,  // TODO: Calculate based on previous period
      customersGrowth: 0, // TODO: Calculate based on previous period
      conversionGrowth: 0 // TODO: Calculate based on previous period
    };
  } catch (error) {
    handleSupabaseError(error, 'fetch dashboard stats');
    throw error; // This line will never be reached due to the throw in handleSupabaseError
  }
}
