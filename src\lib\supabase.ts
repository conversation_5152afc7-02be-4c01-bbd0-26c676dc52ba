import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Image upload utility functions
export async function uploadImage(file: File, folder: string = 'categories'): Promise<string> {
  try {
    // Validate file
    if (!file) {
      throw new Error('No file provided')
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      throw new Error('Invalid file type. Please upload a JPEG, PNG, GIF, or WebP image.')
    }

    // Validate file size (10MB limit)
    const maxSize = 10 * 1024 * 1024 // 10MB in bytes
    if (file.size > maxSize) {
      throw new Error('File size too large. Please upload an image smaller than 10MB.')
    }

    // Generate unique filename
    const fileExt = file.name.split('.').pop()?.toLowerCase() || 'jpg'
    const fileName = `${folder}/${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`

    console.log(`Uploading image: ${fileName}, Size: ${(file.size / 1024 / 1024).toFixed(2)}MB`)

    // Upload file to Supabase Storage
    const { error } = await supabase.storage
      .from('pictures')
      .upload(fileName, file, {
        cacheControl: '3600',
        upsert: false
      })

    if (error) {
      console.error('Supabase storage error:', error)
      throw new Error(`Failed to upload image: ${error.message}`)
    }

    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('pictures')
      .getPublicUrl(fileName)

    console.log(`Image uploaded successfully: ${publicUrl}`)
    return publicUrl
  } catch (error) {
    console.error('Error in uploadImage:', error)
    throw error
  }
}

export async function deleteImage(imageUrl: string): Promise<void> {
  try {
    if (!imageUrl) {
      return
    }

    // Skip deletion for external URLs (not from our storage)
    if (!imageUrl.includes('supabase.co') || !imageUrl.includes('/storage/v1/object/public/pictures/')) {
      console.log('Skipping deletion of external image URL:', imageUrl)
      return
    }

    // Extract file path from URL
    const url = new URL(imageUrl)
    const pathParts = url.pathname.split('/')

    // Find the index of 'pictures' in the path
    const picturesIndex = pathParts.findIndex(part => part === 'pictures')
    if (picturesIndex === -1 || picturesIndex >= pathParts.length - 1) {
      console.error('Invalid image URL format:', imageUrl)
      return
    }

    // Get the file path after 'pictures/'
    const filePath = pathParts.slice(picturesIndex + 1).join('/')

    console.log(`Deleting image: ${filePath}`)

    // Delete file from Supabase Storage
    const { error } = await supabase.storage
      .from('pictures')
      .remove([filePath])

    if (error) {
      console.error('Error deleting image:', error)
      // Don't throw error for delete operations as it's not critical
    } else {
      console.log(`Image deleted successfully: ${filePath}`)
    }
  } catch (error) {
    console.error('Error in deleteImage:', error)
    // Don't throw error for delete operations as it's not critical
  }
}

// Database Types
export interface Database {
  public: {
    Tables: {
      categories: {
        Row: {
          id: string
          name: string
          image: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          image?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          image?: string | null
          updated_at?: string
        }
      }
      products: {
        Row: {
          id: string
          title: string
          description: string
          price: number
          affiliate_earning_price: number
          category_id: string
          main_image: string
          gallery_images: string[] | null
          in_stock: boolean
          stock_count: number
          free_shipping: boolean
          shipping_cost: number
          rating_rate: number
          rating_count: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description: string
          price: number
          affiliate_earning_price: number
          category_id: string
          main_image: string
          gallery_images?: string[] | null
          in_stock?: boolean
          stock_count?: number
          free_shipping?: boolean
          shipping_cost?: number
          rating_rate?: number
          rating_count?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string
          price?: number
          affiliate_earning_price?: number
          category_id?: string
          main_image?: string
          gallery_images?: string[] | null
          in_stock?: boolean
          stock_count?: number
          free_shipping?: boolean
          shipping_cost?: number
          rating_rate?: number
          rating_count?: number
          updated_at?: string
        }
      }
      customers: {
        Row: {
          id: string
          name: string
          email: string
          phone: string
          location: string
          address: string | null
          join_date: string
          total_orders: number
          total_spent: number
          status: 'active' | 'inactive'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          email: string
          phone: string
          location: string
          address?: string | null
          join_date?: string
          total_orders?: number
          total_spent?: number
          status?: 'active' | 'inactive'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          email?: string
          phone?: string
          location?: string
          address?: string | null
          total_orders?: number
          total_spent?: number
          status?: 'active' | 'inactive'
          updated_at?: string
        }
      }
      affiliate_workers: {
        Row: {
          id: string
          name: string
          email: string
          phone: string
          location: string
          commission_rate: number
          total_earnings: number
          join_date: string
          status: 'active' | 'inactive'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          email: string
          phone: string
          location: string
          commission_rate?: number
          total_earnings?: number
          join_date?: string
          status?: 'active' | 'inactive'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          email?: string
          phone?: string
          location?: string
          commission_rate?: number
          total_earnings?: number
          status?: 'active' | 'inactive'
          updated_at?: string
        }
      }
      orders: {
        Row: {
          id: string
          customer_id: string
          affiliate_worker_id: string | null
          order_date: string
          status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled'
          total: number
          items_count: number
          payment_method: string
          shipping_cost: number
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          customer_id: string
          affiliate_worker_id?: string | null
          order_date?: string
          status?: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled'
          total: number
          items_count: number
          payment_method: string
          shipping_cost?: number
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          customer_id?: string
          affiliate_worker_id?: string | null
          status?: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled'
          total?: number
          items_count?: number
          payment_method?: string
          shipping_cost?: number
          notes?: string | null
          updated_at?: string
        }
      }
      order_items: {
        Row: {
          id: string
          order_id: string
          product_id: string
          quantity: number
          price: number
          affiliate_earnings: number
          created_at: string
        }
        Insert: {
          id?: string
          order_id: string
          product_id: string
          quantity: number
          price: number
          affiliate_earnings: number
          created_at?: string
        }
        Update: {
          id?: string
          order_id?: string
          product_id?: string
          quantity?: number
          price?: number
          affiliate_earnings?: number
        }
      }
      delivery_locations: {
        Row: {
          id: string
          wilaya_code: number
          wilaya_name: string
          desk_price: number
          domicile_price: number
          enabled: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          wilaya_code: number
          wilaya_name: string
          desk_price: number
          domicile_price: number
          enabled?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          wilaya_code?: number
          wilaya_name?: string
          desk_price?: number
          domicile_price?: number
          enabled?: boolean
          updated_at?: string
        }
      }
      payments: {
        Row: {
          id: string
          affiliate_worker_id: string
          amount: number
          payment_date: string
          status: 'pending' | 'paid' | 'cancelled'
          payment_method: string | null
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          affiliate_worker_id: string
          amount: number
          payment_date?: string
          status?: 'pending' | 'paid' | 'cancelled'
          payment_method?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          affiliate_worker_id?: string
          amount?: number
          payment_date?: string
          status?: 'pending' | 'paid' | 'cancelled'
          payment_method?: string | null
          notes?: string | null
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
