
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Bar,
  BarChart,
  CartesianGrid,
  Legend,
  ResponsiveContainer,
  Tooltip as Recharts<PERSON>ooltip,
  <PERSON><PERSON>xis,
  YAxis,
} from "recharts";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { formatCurrency } from '@/lib/utils';
import { useIsMobile } from '@/hooks/use-mobile';

// Sample data structure
const monthlyData = [
  { name: "Jan", earnings: 420, referrals: 5 },
  { name: "Feb", earnings: 690, referrals: 8 },
  { name: "Mar", earnings: 1250, referrals: 15 },
  { name: "Apr", earnings: 890, referrals: 10 },
  { name: "May", earnings: 1500, referrals: 17 },
  { name: "Jun", earnings: 2100, referrals: 24 },
];

const weeklyData = [
  { name: "Week 1", earnings: 350, referrals: 4 },
  { name: "Week 2", earnings: 475, referrals: 5 },
  { name: "Week 3", earnings: 620, referrals: 7 },
  { name: "Week 4", earnings: 780, referrals: 9 },
];

const EarningsChart = () => {
  const [period, setPeriod] = useState("monthly");
  const isMobile = useIsMobile();
  const data = period === "monthly" ? monthlyData : weeklyData;

  // For mobile, we'll use a simplified dataset if in monthly view
  const mobileData = period === "monthly"
    ? monthlyData.map(item => ({
        name: item.name.substring(0, 3), // Abbreviate month names
        earnings: item.earnings,
        referrals: item.referrals
      }))
    : weeklyData.map(item => ({
        name: item.name.replace('Week ', 'W'), // Abbreviate week names
        earnings: item.earnings,
        referrals: item.referrals
      }));

  // Choose appropriate chart margins based on screen size
  const chartMargins = isMobile
    ? { top: 20, right: 10, left: 0, bottom: 5 }
    : { top: 20, right: 30, left: 20, bottom: 5 };

  return (
    <Card className="opacity-0 animate-fade-in">
      <CardHeader className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 pb-2">
        <div>
          <CardTitle className="text-lg sm:text-xl">Earnings Overview</CardTitle>
          <CardDescription className="text-xs sm:text-sm">Your commission earnings over time</CardDescription>
        </div>
        <Select
          value={period}
          onValueChange={setPeriod}
        >
          <SelectTrigger className="w-full sm:w-[150px]">
            <SelectValue placeholder="Select period" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="weekly">Last 4 Weeks</SelectItem>
            <SelectItem value="monthly">Last 6 Months</SelectItem>
          </SelectContent>
        </Select>
      </CardHeader>
      <CardContent className="h-[250px] sm:h-[300px]">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={isMobile ? mobileData : data}
            margin={chartMargins}
            barGap={isMobile ? 0 : 4}
            barSize={isMobile ? 12 : 20}
          >
            <CartesianGrid strokeDasharray="3 3" vertical={false} />
            <XAxis
              dataKey="name"
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: isMobile ? 10 : 12 }}
              interval={isMobile ? 0 : 'preserveStartEnd'}
            />
            <YAxis
              yAxisId="left"
              axisLine={false}
              tickLine={false}
              tickFormatter={(value) => isMobile ? `$${value}` : `$${value}`}
              tick={{ fontSize: isMobile ? 10 : 12 }}
              width={isMobile ? 30 : 40}
            />
            {!isMobile && (
              <YAxis
                yAxisId="right"
                orientation="right"
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12 }}
              />
            )}
            <RechartsTooltip
              formatter={(value, name) => {
                return name === "earnings"
                  ? formatCurrency(value as number)
                  : value;
              }}
              wrapperStyle={{ fontSize: isMobile ? '10px' : '12px' }}
            />
            <Legend
              wrapperStyle={{ fontSize: isMobile ? '10px' : '12px' }}
              iconSize={isMobile ? 8 : 10}
            />
            <Bar
              yAxisId="left"
              dataKey="earnings"
              name="Earnings"
              fill="hsl(var(--primary))"
              radius={[4, 4, 0, 0]}
            />
            <Bar
              yAxisId={isMobile ? "left" : "right"}
              dataKey="referrals"
              name="Referrals"
              fill="hsl(var(--accent))"
              radius={[4, 4, 0, 0]}
            />
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};

export default EarningsChart;
