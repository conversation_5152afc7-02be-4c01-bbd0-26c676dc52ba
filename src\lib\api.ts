// API service for Supabase backend
import { supabase, uploadImage, deleteImage } from './supabase';
import type {
  Product,
  Category,
  Customer,
  AffiliateWorker,
  Order,
  OrderItem,
  DeliveryLocation,
  Payment,
  DashboardStats,
  CreateProductForm,
  CreateCategoryForm,
  CreateCustomerForm,
  CreateOrderForm,
  SearchFilters,
  PaginatedResponse
} from '../types';

// Utility function to handle Supabase errors
function handleSupabaseError(error: any, operation: string) {
  console.error(`Error in ${operation}:`, error);
  throw new Error(error.message || `Failed to ${operation}`);
}

// ===== PROFILES API =====

// Function to create a user profile
export async function createProfile(userId: string, profileData: { name: string; role: string; phone?: string }) {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .insert({
        id: userId,
        name: profileData.name,
        role: profileData.role,
        phone: profileData.phone || null,
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    handleSupabaseError(error, 'create profile');
    throw error;
  }
}

// Function to get user profile
export async function getProfile(userId: string) {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .maybeSingle(); // Use maybeSingle instead of single to handle no rows

    if (error) throw error;
    return data; // Will be null if no profile exists
  } catch (error) {
    handleSupabaseError(error, 'get profile');
    throw error;
  }
}

// ===== PRODUCTS API =====

// Function to fetch all products
export async function fetchProducts(): Promise<Product[]> {
  try {
    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        categories!inner(name)
      `)
      .order('created_at', { ascending: false });

    if (error) throw error;

    return data.map(product => ({
      ...product,
      category: product.categories.name,
      rating: {
        rate: product.rating_rate || 0,
        count: product.rating_count || 0
      },
      main_image: product.main_image,
      gallery_images: product.gallery_images || [],
      // Add backward compatibility fields
      image: product.main_image, // For components that still use 'image'
      commission: product.affiliate_earning_price ? (product.affiliate_earning_price / product.price * 100) : 0, // Calculate percentage for backward compatibility
      stockCount: product.stock_count,
      inStock: product.in_stock
    }));
  } catch (error) {
    handleSupabaseError(error, 'fetch products');
    return []; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// Function to fetch a single product by ID
export async function fetchProductById(id: string): Promise<Product> {
  try {
    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        categories!inner(name)
      `)
      .eq('id', id)
      .single();

    if (error) throw error;

    return {
      ...data,
      category: data.categories.name,
      rating: {
        rate: data.rating_rate || 0,
        count: data.rating_count || 0
      },
      main_image: data.main_image,
      gallery_images: data.gallery_images || [],
      // Add backward compatibility fields
      image: data.main_image, // For components that still use 'image'
      commission: data.affiliate_earning_price ? (data.affiliate_earning_price / data.price * 100) : 0, // Calculate percentage for backward compatibility
      stockCount: data.stock_count,
      inStock: data.in_stock
    };
  } catch (error) {
    handleSupabaseError(error, `fetch product with ID ${id}`);
    throw error; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// Function to fetch products in a specific category
export async function fetchProductsByCategory(categoryName: string): Promise<Product[]> {
  try {
    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        categories!inner(name)
      `)
      .eq('categories.name', categoryName)
      .order('created_at', { ascending: false });

    if (error) throw error;

    return data.map(product => ({
      ...product,
      category: product.categories.name,
      rating: {
        rate: product.rating_rate || 0,
        count: product.rating_count || 0
      },
      main_image: product.main_image,
      gallery_images: product.gallery_images || [],
      // Add backward compatibility fields
      image: product.main_image, // For components that still use 'image'
      commission: product.affiliate_earning_price ? (product.affiliate_earning_price / product.price * 100) : 0, // Calculate percentage for backward compatibility
      stockCount: product.stock_count,
      inStock: product.in_stock
    }));
  } catch (error) {
    handleSupabaseError(error, `fetch products in category ${categoryName}`);
    return []; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// Function to create a new product
export async function createProduct(productData: CreateProductForm): Promise<Product> {
  try {
    const { data, error } = await supabase
      .from('products')
      .insert({
        title: productData.title,
        description: productData.description,
        price: productData.price,
        affiliate_earning_price: productData.affiliate_earning_price,
        category_id: productData.category_id,
        main_image: typeof productData.main_image === 'string' ? productData.main_image : '',
        gallery_images: productData.gallery_images?.map(img => typeof img === 'string' ? img : '') || [],
        in_stock: productData.in_stock,
        stock_count: productData.stock_count,
        free_shipping: productData.free_shipping,
        shipping_cost: productData.shipping_cost,
        rating_rate: 0,
        rating_count: 0
      })
      .select()
      .single();

    if (error) throw error;
    return await fetchProductById(data.id);
  } catch (error) {
    handleSupabaseError(error, 'create product');
    throw error; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// Function to update a product
export async function updateProduct(id: string, productData: Partial<CreateProductForm>): Promise<Product> {
  try {
    const updateData: any = {
      ...productData,
      updated_at: new Date().toISOString()
    };

    if (productData.main_image && typeof productData.main_image === 'string') {
      updateData.main_image = productData.main_image;
    }

    if (productData.gallery_images) {
      updateData.gallery_images = productData.gallery_images.map(img => typeof img === 'string' ? img : '');
    }

    const { error } = await supabase
      .from('products')
      .update(updateData)
      .eq('id', id);

    if (error) throw error;
    return await fetchProductById(id);
  } catch (error) {
    handleSupabaseError(error, 'update product');
    throw error; // Re-throw to ensure the error propagates properly
  }
}

// Function to delete a product
export async function deleteProduct(id: string): Promise<void> {
  try {
    const { error } = await supabase
      .from('products')
      .delete()
      .eq('id', id);

    if (error) throw error;
  } catch (error) {
    handleSupabaseError(error, 'delete product');
    throw error; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// ===== CATEGORIES API =====

// Function to fetch all categories
export async function fetchCategories(): Promise<Category[]> {
  try {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .order('name');

    if (error) throw error;
    return data;
  } catch (error) {
    handleSupabaseError(error, 'fetch categories');
    return []; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// Function to get enhanced categories with product counts and potential earnings
export async function getEnhancedCategories(): Promise<(Category & {
  productCount: number;
  potentialEarnings: number;
})[]> {
  try {
    const categories = await fetchCategories();

    const enhancedCategories = await Promise.all(
      categories.map(async (category) => {
        // Count products in this category
        const { count: productCount } = await supabase
          .from('products')
          .select('*', { count: 'exact', head: true })
          .eq('category_id', category.id);

        // Calculate potential earnings from products in this category
        const { data: products } = await supabase
          .from('products')
          .select('affiliate_earning_price')
          .eq('category_id', category.id);

        const potentialEarnings = products?.reduce((total, product) => {
          return total + product.affiliate_earning_price;
        }, 0) || 0;

        return {
          ...category,
          productCount: productCount || 0,
          potentialEarnings: Math.round(potentialEarnings * 100) / 100
        };
      })
    );

    return enhancedCategories;
  } catch (error) {
    handleSupabaseError(error, 'get enhanced categories');
    return []; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// Function to create a new category
export async function createCategory(categoryData: CreateCategoryForm): Promise<Category> {
  try {
    let imageUrl: string | null = null;

    // Handle image upload if file is provided
    if (categoryData.image instanceof File) {
      imageUrl = await uploadImage(categoryData.image, 'categories');
    } else if (typeof categoryData.image === 'string') {
      imageUrl = categoryData.image;
    }

    const { data, error } = await supabase
      .from('categories')
      .insert({
        name: categoryData.name,
        image: imageUrl
      })
      .select()
      .single();

    if (error) {
      // If database insert fails and we uploaded an image, clean it up
      if (imageUrl && categoryData.image instanceof File) {
        await deleteImage(imageUrl);
      }
      throw error;
    }

    return data;
  } catch (error) {
    handleSupabaseError(error, 'create category');
    throw error; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// Function to update a category
export async function updateCategory(id: string, categoryData: Partial<CreateCategoryForm>): Promise<Category> {
  try {
    // Get current category data to handle image replacement
    const { data: currentCategory, error: fetchError } = await supabase
      .from('categories')
      .select('image')
      .eq('id', id)
      .single();

    if (fetchError) throw fetchError;

    const updateData: any = {
      ...categoryData,
      updated_at: new Date().toISOString()
    };

    let newImageUrl: string | null = null;
    let oldImageUrl: string | null = currentCategory?.image || null;

    // Handle image upload if file is provided
    if (categoryData.image instanceof File) {
      newImageUrl = await uploadImage(categoryData.image, 'categories');
      updateData.image = newImageUrl;
    } else if (typeof categoryData.image === 'string') {
      updateData.image = categoryData.image;
      newImageUrl = categoryData.image;
    }

    const { data, error } = await supabase
      .from('categories')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      // If database update fails and we uploaded a new image, clean it up
      if (newImageUrl && categoryData.image instanceof File) {
        await deleteImage(newImageUrl);
      }
      throw error;
    }

    // If update was successful and we uploaded a new image, delete the old one
    if (newImageUrl && oldImageUrl && oldImageUrl !== newImageUrl && categoryData.image instanceof File) {
      await deleteImage(oldImageUrl);
    }

    return data;
  } catch (error) {
    handleSupabaseError(error, 'update category');
    throw error; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// Function to delete a category
export async function deleteCategory(id: string): Promise<void> {
  try {
    // Get category data to clean up image
    const { data: category, error: fetchError } = await supabase
      .from('categories')
      .select('image')
      .eq('id', id)
      .single();

    if (fetchError) throw fetchError;

    const { error } = await supabase
      .from('categories')
      .delete()
      .eq('id', id);

    if (error) throw error;

    // Clean up image after successful deletion
    if (category?.image) {
      await deleteImage(category.image);
    }
  } catch (error) {
    handleSupabaseError(error, 'delete category');
    throw error; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// ===== ENHANCED PRODUCT FUNCTIONS =====

// Function to get enhanced products (for backward compatibility)
export async function getEnhancedProducts(): Promise<Product[]> {
  return await fetchProducts();
}

// Function to get an enhanced product by ID (for backward compatibility)
export async function getEnhancedProductById(id: string): Promise<Product> {
  return await fetchProductById(id);
}

// Function to get enhanced products by category (for backward compatibility)
export async function getEnhancedProductsByCategory(categoryName: string): Promise<Product[]> {
  return await fetchProductsByCategory(categoryName);
}

// ===== CUSTOMERS API =====

// Function to fetch all customers
export async function fetchCustomers(): Promise<Customer[]> {
  try {
    const { data, error } = await supabase
      .from('customers')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  } catch (error) {
    handleSupabaseError(error, 'fetch customers');
    return []; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// Function to fetch a single customer by ID
export async function fetchCustomerById(id: string): Promise<Customer> {
  try {
    const { data, error } = await supabase
      .from('customers')
      .select('*')
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    handleSupabaseError(error, `fetch customer with ID ${id}`);
    throw error; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// Function to create a new customer
export async function createCustomer(customerData: CreateCustomerForm): Promise<Customer> {
  try {
    const { data, error } = await supabase
      .from('customers')
      .insert({
        name: customerData.name,
        email: customerData.email,
        phone: customerData.phone,
        location: customerData.location,
        address: customerData.address,
        join_date: new Date().toISOString(),
        total_orders: 0,
        total_spent: 0,
        status: 'active'
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    handleSupabaseError(error, 'create customer');
    throw error; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// Function to update a customer
export async function updateCustomer(id: string, customerData: Partial<CreateCustomerForm>): Promise<Customer> {
  try {
    const { data, error } = await supabase
      .from('customers')
      .update({
        ...customerData,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    handleSupabaseError(error, 'update customer');
    throw error; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// Function to delete a customer
export async function deleteCustomer(id: string): Promise<void> {
  try {
    const { error } = await supabase
      .from('customers')
      .delete()
      .eq('id', id);

    if (error) throw error;
  } catch (error) {
    handleSupabaseError(error, 'delete customer');
    throw error; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// Re-export extended API functions
export * from './api-extended';
